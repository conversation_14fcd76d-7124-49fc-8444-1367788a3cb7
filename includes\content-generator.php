<?php
/**
 * Content generator functions for Auto Blog Create plugin.
 *
 * @package Auto_Blog_Create
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Generate content using OpenRouter Deepseek R1 API with Wikipedia fallback.
 *
 * @param string $topic The topic to generate content about.
 * @param string $content_type The content type ('travel' or 'mental_health').
 * @param string $section The section of the post (e.g., 'introduction', 'main_content', 'conclusion').
 * @return string The generated content.
 */
function abc_generate_ai_content($topic, $content_type = 'travel', $section = '') {
    // Check if we should use Wikipedia directly
    $use_wikipedia = get_option('abc_use_wikipedia', false);

    if (!$use_wikipedia) {
        // Get OpenRouter API key
        $api_key = get_option('abc_openrouter_api_key');

        if (!$api_key) {
            abc_log_error('OpenRouter API key not configured, falling back to Wikipedia');
            return abc_get_fallback_content($topic, $section, $content_type);
        }

        // Get the selected Deepseek model
        $model = get_option('abc_deepseek_model', 'deepseek/deepseek-r1');

        // Prepare the prompt based on content type and section
        $prompt = abc_get_content_prompt($topic, $content_type, $section);

        // Make API request to OpenRouter with retry logic
        $api_url = 'https://openrouter.ai/api/v1/chat/completions';
        $max_retries = 3;
        $retry_count = 0;
        $response = null;

        while ($retry_count < $max_retries) {
            $body_data = array(
                'model' => $model,
                'messages' => array(
                    array(
                        'role' => 'system',
                        'content' => abc_get_system_prompt($content_type)
                    ),
                    array(
                        'role' => 'user',
                        'content' => $prompt
                    )
                ),
                'max_tokens' => 1000,
                'temperature' => 0.7,
                'top_p' => 0.9
            );

            $args = array(
                'body'    => json_encode($body_data),
                'headers' => array(
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $api_key,
                    'HTTP-Referer' => home_url(),
                    'X-Title' => get_bloginfo('name')
                ),
                'timeout' => 30, // Reduced timeout
                'sslverify' => false, // Disable SSL verification for faster response
            );

            $response = wp_remote_post($api_url, $args);

            // If successful, break the loop
            if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
                break;
            }

            // Log retry attempt
            abc_log_error('OpenRouter API retry attempt ' . ($retry_count + 1) . ' of ' . $max_retries);
            
            // Wait before retrying (exponential backoff)
            sleep(pow(2, $retry_count));
            $retry_count++;
        }

        // Check for errors after all retries
        if (!is_wp_error($response)) {
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);

            // Check if we got a valid response
            if (!empty($data) && isset($data['choices'][0]['message']['content'])) {
                $generated_text = $data['choices'][0]['message']['content'];

                // Clean up the generated text
                $generated_text = abc_clean_generated_text($generated_text, $prompt);

                // If we got a reasonable amount of text, return it
                if (strlen($generated_text) > 100) {
                    return $generated_text;
                }
            } else {
                abc_log_error('Invalid response from OpenRouter API after ' . $max_retries . ' retries: ' . $body);
            }
        } else {
            abc_log_error('OpenRouter API error after ' . $max_retries . ' retries: ' . $response->get_error_message());
        }
    }

    // If we get here, either force_fallback was true or the API request failed
    // Log that we're using the fallback
    abc_log_error('Using fallback content generation for: ' . $topic . ', section: ' . $section);

    // Try Wikipedia fallback
    return abc_get_fallback_content($topic, $section, $content_type);
}

/**
 * Get the appropriate prompt for a specific section.
 *
 * @param string $topic The topic to generate content about.
 * @param string $section The section of the post.
 * @return string The prompt for the API.
 */
function abc_get_section_prompt($topic, $section) {
    switch ($section) {
        case 'benefits':
            return "Write a detailed paragraph about the health benefits of $topic. Include information about physical, mental, and long-term health advantages.";

        case 'getting_started':
            return "Write a detailed paragraph about how to get started with $topic. Include information for beginners, basic equipment needed, and first steps.";

        case 'exercises':
            return "Write a list of the top 5 essential exercises for $topic. For each exercise, provide a brief description of proper form and benefits.";

        case 'nutrition':
            return "Write a detailed paragraph about nutrition considerations for $topic. Include dietary recommendations, timing, and supplements if relevant.";

        case 'common_mistakes':
            return "Write a detailed paragraph about common mistakes people make with $topic. Include tips on how to avoid these mistakes and improve results.";

        default:
            return "Write a comprehensive fitness guide about $topic including benefits, how to get started, key exercises, nutrition tips, and common mistakes to avoid.";
    }
}

/**
 * Get system prompt for different content types.
 *
 * @param string $content_type The content type ('travel' or 'mental_health').
 * @return string The system prompt.
 */
function abc_get_system_prompt($content_type) {
    switch ($content_type) {
        case 'travel':
            return "You are an expert travel writer and blogger. Create engaging, informative, and SEO-optimized travel content that inspires readers to explore new destinations. Focus on practical tips, cultural insights, and unique experiences. Write in a friendly, enthusiastic tone that makes readers excited about traveling.";

        case 'mental_health':
            return "You are a mental health advocate and wellness expert. Create compassionate, evidence-based, and helpful mental health content that supports readers' wellbeing. Focus on practical strategies, self-care tips, and promoting mental wellness. Write in a supportive, understanding tone while being careful to recommend professional help when appropriate.";

        default:
            return "You are an expert content writer. Create engaging, informative, and SEO-optimized blog content that provides value to readers.";
    }
}

/**
 * Get the appropriate prompt for content generation.
 *
 * @param string $topic The topic.
 * @param string $content_type The content type ('travel' or 'mental_health').
 * @param string $section The section.
 * @return string The prompt.
 */
function abc_get_content_prompt($topic, $content_type, $section) {
    if ($content_type === 'travel') {
        return abc_get_travel_prompt($topic, $section);
    } elseif ($content_type === 'mental_health') {
        return abc_get_mental_health_prompt($topic, $section);
    }

    return "Write comprehensive, SEO-optimized content about $topic.";
}

/**
 * Get travel-specific prompts.
 *
 * @param string $topic The travel topic.
 * @param string $section The section.
 * @return string The prompt.
 */
function abc_get_travel_prompt($topic, $section) {
    switch ($section) {
        case 'introduction':
            return "Write an engaging introduction about traveling to $topic. Include what makes this destination special, who should visit, and what they can expect. Make it exciting and inspiring.";

        case 'attractions':
            return "Write about the top 5-7 must-see attractions and activities in $topic. Include practical details like opening hours, costs, and insider tips for each attraction.";

        case 'accommodation':
            return "Write about accommodation options in $topic. Include different budget ranges, recommended areas to stay, and tips for booking the best places.";

        case 'food':
            return "Write about the local cuisine and food scene in $topic. Include must-try dishes, recommended restaurants, and food experiences unique to this destination.";

        case 'transportation':
            return "Write about transportation options in $topic. Include how to get there, how to get around locally, and money-saving transportation tips.";

        case 'budget':
            return "Write a comprehensive budget guide for traveling to $topic. Include costs for accommodation, food, activities, and transportation with tips for saving money.";

        default:
            return "Write a comprehensive travel guide about $topic including top attractions, where to stay, local cuisine, transportation options, and budget tips. Make it practical and inspiring for travelers.";
    }
}

/**
 * Get mental health-specific prompts.
 *
 * @param string $topic The mental health topic.
 * @param string $section The section.
 * @return string The prompt.
 */
function abc_get_mental_health_prompt($topic, $section) {
    switch ($section) {
        case 'introduction':
            return "Write a compassionate introduction about $topic. Explain what it is, why it's important, and how it affects people's daily lives. Be supportive and understanding.";

        case 'symptoms':
            return "Write about the common signs and symptoms of $topic. Help readers understand what to look for and when to seek professional help. Be informative but not diagnostic.";

        case 'coping_strategies':
            return "Write about practical coping strategies and techniques for managing $topic. Include evidence-based approaches, self-care tips, and daily practices that can help.";

        case 'professional_help':
            return "Write about when and how to seek professional help for $topic. Include types of therapy, how to find a therapist, and what to expect from treatment.";

        case 'lifestyle':
            return "Write about lifestyle factors that can impact $topic. Include diet, exercise, sleep, and other daily habits that can support mental wellness.";

        case 'support':
            return "Write about building support systems and finding community when dealing with $topic. Include family, friends, support groups, and online resources.";

        default:
            return "Write a comprehensive, supportive guide about $topic including understanding the condition, coping strategies, when to seek help, and lifestyle factors that can help. Always encourage professional support when needed.";
    }
}

/**
 * Clean up the generated text.
 *
 * @param string $text The generated text.
 * @param string $prompt The original prompt.
 * @return string The cleaned text.
 */
function abc_clean_generated_text($text, $prompt) {
    // Remove the prompt from the beginning if it's included
    if (strpos($text, $prompt) === 0) {
        $text = substr($text, strlen($prompt));
    }

    // Trim whitespace
    $text = trim($text);

    // Remove any incomplete sentences at the end
    $last_period_pos = strrpos($text, '.');
    if ($last_period_pos !== false) {
        $text = substr($text, 0, $last_period_pos + 1);
    }

    return $text;
}

/**
 * Get Wikipedia content for a topic.
 *
 * @param string $topic The topic to search for on Wikipedia.
 * @return array|bool Array with Wikipedia content or false on failure.
 */
function abc_get_wikipedia_content($topic) {
    // Prepare the API URL
    $api_url = 'https://en.wikipedia.org/w/api.php';

    // First, search for the page
    $search_args = array(
        'action'   => 'query',
        'list'     => 'search',
        'srsearch' => $topic,
        'format'   => 'json',
    );

    $search_url = add_query_arg($search_args, $api_url);
    $search_response = wp_remote_get($search_url, array(
        'headers' => array(
            'User-Agent' => 'WordPress/Auto-Blog-Create-Plugin',
        ),
    ));

    if (is_wp_error($search_response)) {
        abc_log_error('Wikipedia search API error: ' . $search_response->get_error_message());
        return false;
    }

    $search_body = wp_remote_retrieve_body($search_response);
    $search_data = json_decode($search_body, true);

    if (empty($search_data) || !isset($search_data['query']['search'][0]['title'])) {
        abc_log_error('No Wikipedia results found for: ' . $topic);
        return false;
    }

    // Get the page title from search results
    $page_title = $search_data['query']['search'][0]['title'];

    // Now get the page content
    $content_args = array(
        'action'    => 'query',
        'prop'      => 'extracts|info',
        'exintro'   => 1,
        'explaintext' => 1,
        'inprop'    => 'url',
        'titles'    => $page_title,
        'format'    => 'json',
    );

    $content_url = add_query_arg($content_args, $api_url);
    $content_response = wp_remote_get($content_url, array(
        'headers' => array(
            'User-Agent' => 'WordPress/Auto-Blog-Create-Plugin',
        ),
    ));

    if (is_wp_error($content_response)) {
        abc_log_error('Wikipedia content API error: ' . $content_response->get_error_message());
        return false;
    }

    $content_body = wp_remote_retrieve_body($content_response);
    $content_data = json_decode($content_body, true);

    if (empty($content_data) || !isset($content_data['query']['pages'])) {
        abc_log_error('Failed to get Wikipedia content for: ' . $page_title);
        return false;
    }

    // Extract the page content
    $pages = $content_data['query']['pages'];
    $page = reset($pages);

    if (!isset($page['extract']) || empty($page['extract'])) {
        abc_log_error('No extract found in Wikipedia content for: ' . $page_title);
        return false;
    }

    // Get the page URL
    $page_url = isset($page['fullurl']) ? $page['fullurl'] : '';

    // Get sections for the page
    $sections_args = array(
        'action'    => 'parse',
        'page'      => $page_title,
        'prop'      => 'sections',
        'format'    => 'json',
    );

    $sections_url = add_query_arg($sections_args, $api_url);
    $sections_response = wp_remote_get($sections_url, array(
        'headers' => array(
            'User-Agent' => 'WordPress/Auto-Blog-Create-Plugin',
        ),
    ));

    $sections = array();

    if (!is_wp_error($sections_response)) {
        $sections_body = wp_remote_retrieve_body($sections_response);
        $sections_data = json_decode($sections_body, true);

        if (!empty($sections_data) && isset($sections_data['parse']['sections'])) {
            $sections = $sections_data['parse']['sections'];
        }
    }

    return array(
        'title'    => $page_title,
        'extract'  => $page['extract'],
        'url'      => $page_url,
        'sections' => $sections,
    );
}

/**
 * Get section content from Wikipedia.
 *
 * @param string $page_title The Wikipedia page title.
 * @param int $section_index The section index.
 * @return string|bool The section content or false on failure.
 */
function abc_get_wikipedia_section($page_title, $section_index) {
    // Prepare the API URL
    $api_url = 'https://en.wikipedia.org/w/api.php';

    $args = array(
        'action'    => 'parse',
        'page'      => $page_title,
        'section'   => $section_index,
        'prop'      => 'text',
        'format'    => 'json',
    );

    $url = add_query_arg($args, $api_url);
    $response = wp_remote_get($url, array(
        'headers' => array(
            'User-Agent' => 'WordPress/Auto-Blog-Create-Plugin',
        ),
    ));

    if (is_wp_error($response)) {
        abc_log_error('Wikipedia section API error: ' . $response->get_error_message());
        return false;
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    if (empty($data) || !isset($data['parse']['text']['*'])) {
        abc_log_error('Failed to get Wikipedia section content for: ' . $page_title . ', section: ' . $section_index);
        return false;
    }

    // Extract the HTML content
    $html_content = $data['parse']['text']['*'];

    // Convert HTML to plain text
    $text_content = wp_strip_all_tags($html_content);

    // Clean up the text
    $text_content = str_replace('[edit]', '', $text_content);
    $text_content = preg_replace('/\[\d+\]/', '', $text_content); // Remove reference numbers
    $text_content = trim($text_content);

    return $text_content;
}

/**
 * Get fallback content when API fails.
 *
 * @param string $topic The topic to generate content about.
 * @param string $section The section of the post.
 * @param string $content_type The content type ('travel', 'mental_health', or 'fitness').
 * @return string The fallback content.
 */
function abc_get_fallback_content($topic, $section, $content_type = 'fitness') {
    // Try to get content from Wikipedia first
    $wiki_content = abc_get_wikipedia_content($topic);

    if ($wiki_content) {
        // Extract intro content
        $intro = $wiki_content['extract'];

        // Look for relevant sections based on the requested section
        $relevant_section = null;
        $section_keywords = array();

        switch ($section) {
            case 'benefits':
                $section_keywords = array('benefit', 'health', 'advantage', 'improvement', 'wellness');
                break;

            case 'getting_started':
                $section_keywords = array('beginner', 'start', 'introduction', 'basic', 'equipment', 'preparation');
                break;

            case 'exercises':
                $section_keywords = array('exercise', 'workout', 'movement', 'training', 'routine', 'technique');
                break;

            case 'nutrition':
                $section_keywords = array('nutrition', 'diet', 'food', 'supplement', 'meal', 'eating');
                break;

            case 'common_mistakes':
                $section_keywords = array('mistake', 'error', 'avoid', 'wrong', 'problem', 'issue');
                break;
        }

        // Search for a relevant section
        foreach ($wiki_content['sections'] as $wiki_section) {
            $section_title = strtolower($wiki_section['line']);

            foreach ($section_keywords as $keyword) {
                if (strpos($section_title, $keyword) !== false) {
                    $relevant_section = $wiki_section;
                    break 2;
                }
            }
        }

        // If we found a relevant section, get its content
        if ($relevant_section) {
            $section_content = abc_get_wikipedia_section($wiki_content['title'], $relevant_section['index']);

            if ($section_content) {
                // Add attribution
                $section_content .= "\n\nSource: <a href=\"" . esc_url($wiki_content['url']) . "\" target=\"_blank\">Wikipedia</a>";
                return $section_content;
            }
        }

        // If no relevant section was found or couldn't be retrieved, use the intro
        if (!empty($intro)) {
            // Add attribution
            $intro .= "\n\nSource: <a href=\"" . esc_url($wiki_content['url']) . "\" target=\"_blank\">Wikipedia</a>";
            return $intro;
        }
    }

    // If Wikipedia content retrieval failed, use basic fallback content based on content type
    if ($content_type === 'travel') {
        return abc_get_travel_fallback_content($topic, $section);
    } elseif ($content_type === 'mental_health') {
        return abc_get_mental_health_fallback_content($topic, $section);
    } else {
        // Default fitness fallback content
        switch ($section) {
            case 'benefits':
                return "The health benefits of $topic are numerous and well-documented. Regular practice can improve cardiovascular health, increase strength and flexibility, boost mental well-being, and enhance overall quality of life. Research shows that consistent engagement with $topic can lead to long-term health improvements and reduced risk of chronic diseases.";

            case 'getting_started':
                return "Getting started with $topic is easier than you might think. Begin with basic movements and gradually increase intensity as your fitness level improves. Essential equipment is minimal and affordable. Start with short sessions and focus on proper form rather than intensity. Consider consulting with a fitness professional for personalized guidance.";

            case 'exercises':
                return "Here are some essential exercises for $topic that you should include in your routine:\n\n1. Basic movement patterns that target major muscle groups\n2. Progressive exercises that build strength and endurance\n3. Flexibility and mobility work for injury prevention\n4. Core strengthening exercises for stability\n5. Functional movements that translate to daily activities";

            case 'nutrition':
                return "Proper nutrition plays a crucial role in maximizing the benefits of $topic. Focus on a balanced diet rich in whole foods, adequate protein for muscle recovery, and proper hydration. Timing of meals around your workout sessions can optimize performance and recovery. Consider consulting with a nutritionist for personalized dietary recommendations.";

            case 'common_mistakes':
                return "Common mistakes with $topic include doing too much too soon, neglecting proper form, skipping warm-up and cool-down periods, and not allowing adequate recovery time. Avoid comparing yourself to others and focus on your own progress. Consistency is more important than intensity, especially when starting out.";

            default:
                return "This fitness guide provides an overview of $topic. For the most accurate and up-to-date information, we recommend consulting with certified fitness professionals, recent scientific studies, or reputable health organizations. Always consult with your healthcare provider before starting any new fitness program.";
        }
    }
}

/**
 * Get travel-specific fallback content.
 *
 * @param string $topic The travel topic.
 * @param string $section The section.
 * @return string The fallback content.
 */
function abc_get_travel_fallback_content($topic, $section) {
    switch ($section) {
        case 'introduction':
            return "Discover the wonders of $topic, a destination that offers unique experiences for every type of traveler. Whether you're seeking adventure, relaxation, culture, or natural beauty, $topic has something special to offer. This comprehensive guide will help you plan your perfect trip.";

        case 'attractions':
            return "The top attractions in $topic offer a diverse range of experiences. From historical landmarks and cultural sites to natural wonders and modern attractions, visitors can explore museums, parks, architectural marvels, and local hotspots that showcase the unique character of this destination.";

        case 'accommodation':
            return "Accommodation options in $topic cater to all budgets and preferences. From luxury hotels and boutique properties to budget-friendly hostels and vacation rentals, travelers can find comfortable lodging in prime locations with easy access to major attractions and transportation.";

        case 'food':
            return "The culinary scene in $topic reflects the local culture and traditions. Visitors can enjoy authentic local dishes, street food, fine dining restaurants, and unique food experiences that showcase regional flavors and cooking techniques passed down through generations.";

        case 'transportation':
            return "Getting to and around $topic is convenient with various transportation options. Public transit, taxis, rental cars, and walking are all viable ways to explore the destination. Planning your transportation in advance can help you save money and time during your visit.";

        case 'budget':
            return "Traveling to $topic can be affordable with proper planning. Budget considerations include accommodation, meals, transportation, activities, and shopping. By researching costs in advance and looking for deals, travelers can enjoy a memorable trip without overspending.";

        default:
            return "This travel guide provides essential information about visiting $topic. For the most current travel advisories, visa requirements, and local conditions, consult official tourism websites and government travel resources before your trip.";
    }
}

/**
 * Get mental health-specific fallback content.
 *
 * @param string $topic The mental health topic.
 * @param string $section The section.
 * @return string The fallback content.
 */
function abc_get_mental_health_fallback_content($topic, $section) {
    switch ($section) {
        case 'introduction':
            return "Understanding $topic is an important step toward better mental health and wellbeing. This condition affects many people and can impact daily life, relationships, and overall quality of life. With proper support, information, and professional guidance, individuals can learn to manage and improve their mental health.";

        case 'symptoms':
            return "Recognizing the signs and symptoms of $topic can help individuals seek appropriate support. Common indicators may include changes in mood, behavior, sleep patterns, appetite, and daily functioning. It's important to remember that symptoms can vary greatly between individuals and professional evaluation is recommended.";

        case 'coping_strategies':
            return "Effective coping strategies for $topic include mindfulness practices, regular exercise, maintaining social connections, establishing healthy routines, and stress management techniques. These approaches can complement professional treatment and help individuals build resilience and emotional regulation skills.";

        case 'professional_help':
            return "Seeking professional help for $topic is a sign of strength and self-care. Mental health professionals, including therapists, counselors, and psychiatrists, can provide evidence-based treatments, therapy, and support. Don't hesitate to reach out when you need assistance.";

        case 'lifestyle':
            return "Lifestyle factors play a significant role in managing $topic. Regular sleep schedules, balanced nutrition, physical activity, stress reduction, and avoiding harmful substances can all contribute to better mental health outcomes and overall wellbeing.";

        case 'support':
            return "Building a strong support system is crucial when dealing with $topic. This may include family, friends, support groups, online communities, and mental health professionals. Remember that you're not alone, and reaching out for support is an important part of the healing process.";

        default:
            return "This mental health guide provides general information about $topic. For personalized advice, diagnosis, or treatment, please consult with qualified mental health professionals. If you're experiencing a mental health crisis, contact emergency services or a crisis helpline immediately.";
    }
}

/**
 * Test the OpenRouter API connection.
 *
 * @return array Result of the test with 'success' boolean and 'message' string.
 */
function abc_test_openrouter_api() {
    $api_key = get_option('abc_openrouter_api_key');

    if (!$api_key) {
        return array(
            'success' => false,
            'message' => 'OpenRouter API key not configured.',
        );
    }

    $model = get_option('abc_deepseek_model', 'deepseek/deepseek-r1');
    $api_url = 'https://openrouter.ai/api/v1/chat/completions';

    $body_data = array(
        'model' => $model,
        'messages' => array(
            array(
                'role' => 'user',
                'content' => 'Test connection. Please respond with "Connection successful!"'
            )
        ),
        'max_tokens' => 50,
        'temperature' => 0.1
    );

    $args = array(
        'body'    => json_encode($body_data),
        'headers' => array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
            'HTTP-Referer' => home_url(),
            'X-Title' => get_bloginfo('name')
        ),
        'timeout' => 30,
    );

    $response = wp_remote_post($api_url, $args);

    if (is_wp_error($response)) {
        return array(
            'success' => false,
            'message' => 'Error: ' . $response->get_error_message(),
        );
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    if (empty($data) || !isset($data['choices'][0]['message']['content'])) {
        return array(
            'success' => false,
            'message' => 'Invalid response from API: ' . $body,
        );
    }

    return array(
        'success' => true,
        'message' => 'Connection successful! Model: ' . $model,
    );
}

/**
 * Test the Hugging Face API connection.
 *
 * @return array Result of the test with 'success' boolean and 'message' string.
 */
function abc_test_huggingface_api() {
    $model = get_option('abc_huggingface_model', 'sshleifer/tiny-gpt2');
    $api_url = 'https://api-inference.huggingface.co/models/' . $model;

    $args = array(
        'body'    => json_encode(array('inputs' => 'Test connection to Hugging Face API.')),
        'headers' => array(
            'Content-Type' => 'application/json',
        ),
        'timeout' => 10,
    );

    $response = wp_remote_post($api_url, $args);

    if (is_wp_error($response)) {
        return array(
            'success' => false,
            'message' => 'Error: ' . $response->get_error_message(),
        );
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body);

    if (empty($data) || !isset($data[0]->generated_text)) {
        return array(
            'success' => false,
            'message' => 'Invalid response from API: ' . $body,
        );
    }

    return array(
        'success' => true,
        'message' => 'Connection successful!',
    );
}

/**
 * Log errors for debugging.
 *
 * @param string $message The error message to log.
 */
function abc_log_error($message) {
    if (WP_DEBUG === true) {
        error_log('Auto Blog Create: ' . $message);
    }
}
