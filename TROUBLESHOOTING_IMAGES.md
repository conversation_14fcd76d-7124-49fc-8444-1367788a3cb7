# Troubleshooting: No Images in Blog Posts

## Quick Diagnosis

If images aren't showing in your blog posts after updating to Pexels API, follow these steps:

### 1. ✅ **Check API Key Configuration**
- Go to **Auto Blog Create → Settings**
- Verify your Pexels API key is entered correctly
- Click **"Test Pexels API"** button to verify connection
- If test fails, get a new API key from https://www.pexels.com/api/

### 2. 🔍 **Enable Debug Mode**
Add this to your `wp-config.php` file:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### 3. 🧪 **Run Debug Tool**
1. Upload `debug-pexels-issue.php` to your WordPress root directory
2. Access it via browser: `yoursite.com/debug-pexels-issue.php`
3. Follow the step-by-step diagnosis
4. **Delete the file after testing**

## Common Issues & Solutions

### ❌ **Issue 1: Invalid API Key**
**Symptoms:** API test fails, error logs show "401 Unauthorized"
**Solution:**
1. Get a new API key from https://www.pexels.com/api/
2. Make sure you're using the API key, not the access token
3. Copy the key exactly without extra spaces

### ❌ **Issue 2: No Images Found**
**Symptoms:** API test passes but no images in posts
**Solution:**
1. Check error logs for "No images found from Pexels API"
2. Try broader search terms (the plugin searches for "destination + travel")
3. Temporarily disable orientation filter by editing the code

### ❌ **Issue 3: Image Download Fails**
**Symptoms:** Images found but not attached to posts
**Solution:**
1. Check WordPress upload directory permissions (should be 755 or 775)
2. Verify server can download external files
3. Check if security plugins are blocking external requests
4. Increase PHP memory limit and execution time

### ❌ **Issue 4: Featured Image Not Set**
**Symptoms:** Images uploaded but not showing as featured image
**Solution:**
1. Check if theme supports featured images: `add_theme_support('post-thumbnails');`
2. Verify post type supports thumbnails
3. Check for plugin conflicts

### ❌ **Issue 5: Server/Firewall Issues**
**Symptoms:** Connection timeouts or blocked requests
**Solution:**
1. Whitelist Pexels domains: `api.pexels.com`, `images.pexels.com`
2. Check server firewall settings
3. Contact hosting provider about external API access

## Step-by-Step Debugging

### Step 1: Test API Connection
```php
// In WordPress admin, go to Auto Blog Create → Settings
// Click "Test Pexels API" button
// Should show: "Connection successful! Retrieved image by [photographer name]"
```

### Step 2: Check Error Logs
Look for these error messages in your WordPress error log:
- `Auto Blog Create: Pexels API key not configured`
- `Auto Blog Create: No images found from Pexels API`
- `Auto Blog Create: Error attaching image`

### Step 3: Manual Test
1. Generate a test post with destination "Paris"
2. Check if the post has a featured image
3. Look in Media Library for uploaded images
4. Check error logs for any new errors

### Step 4: Verify Image URLs
1. Check if Pexels image URLs are accessible in browser
2. Verify download URLs work correctly
3. Test with different destinations

## Advanced Debugging

### Enable Verbose Logging
The updated code now includes detailed logging. Check your error log for:
- Search queries being sent to Pexels
- API responses and status codes
- Image attachment attempts and results

### Check WordPress Requirements
- PHP version 7.4 or higher
- WordPress 5.0 or higher
- `allow_url_fopen` enabled
- Sufficient memory limit (256MB recommended)

### Test with Different Destinations
Try these test destinations:
- Paris (popular, should have many images)
- Tokyo (popular, should have many images)
- London (popular, should have many images)

## Quick Fixes

### Fix 1: Reset API Settings
1. Delete the current API key
2. Save settings
3. Add the API key again
4. Test connection

### Fix 2: Clear Plugin Cache
1. Deactivate the plugin
2. Reactivate the plugin
3. Reconfigure settings

### Fix 3: Check Theme Compatibility
Add this to your theme's `functions.php`:
```php
add_theme_support('post-thumbnails');
```

### Fix 4: Increase Timeouts
Add this to `wp-config.php`:
```php
define('WP_HTTP_BLOCK_EXTERNAL', false);
ini_set('max_execution_time', 300);
ini_set('memory_limit', '256M');
```

## Getting Help

If none of these solutions work:

1. **Check Error Logs:** Look for specific error messages
2. **Test with Debug Tool:** Use the provided debug script
3. **Try Fallback:** Temporarily enable "Force Wikipedia fallback" in settings
4. **Contact Support:** Provide error log details and debug tool results

## Prevention

To avoid future issues:
- Keep your Pexels API key secure
- Monitor API usage limits
- Regularly test the API connection
- Keep WordPress and plugins updated
- Maintain proper file permissions

---

**Remember:** Always delete debug files after testing for security reasons!
