<?php
/**
 * Admin functions for Auto Blog Create plugin.
 *
 * @package Auto_Blog_Create
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Include admin page functions
require_once ABC_ADMIN_DIR . 'admin-page.php';

/**
 * Enqueue admin scripts and styles.
 */
function abc_enqueue_admin_scripts($hook) {
    // Only enqueue on plugin admin pages
    if (strpos($hook, 'auto-blog-create') === false) {
        return;
    }

    wp_enqueue_style(
        'abc-admin-style',
        ABC_PLUGIN_URL . 'admin/css/admin-style.css',
        array(),
        ABC_VERSION
    );

    wp_enqueue_script(
        'abc-admin-script',
        ABC_PLUGIN_URL . 'admin/js/admin-script.js',
        array('jquery'),
        ABC_VERSION,
        true
    );

    // Add localized script data
    wp_localize_script(
        'abc-admin-script',
        'abcData',
        array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce'   => wp_create_nonce('abc_ajax_nonce'),
        )
    );
}
add_action('admin_enqueue_scripts', 'abc_enqueue_admin_scripts');

/**
 * Add settings link to plugin page.
 */
function abc_add_settings_link($links) {
    $settings_link = '<a href="' . admin_url('admin.php?page=auto-blog-create-settings') . '">' . __('Settings', 'auto-blog-create') . '</a>';
    array_unshift($links, $settings_link);
    return $links;
}
add_filter('plugin_action_links_' . plugin_basename(ABC_PLUGIN_DIR . 'auto-blog-create.php'), 'abc_add_settings_link');

/**
 * Add meta box for auto-generated posts.
 */
function abc_add_meta_boxes() {
    add_meta_box(
        'abc_meta_box',
        __('Auto Blog Create Info', 'auto-blog-create'),
        'abc_meta_box_callback',
        'post',
        'side',
        'high'
    );
}
add_action('add_meta_boxes', 'abc_add_meta_boxes');

/**
 * Meta box callback function.
 */
function abc_meta_box_callback($post) {
    // Add nonce for security
    wp_nonce_field('abc_meta_box_nonce', 'abc_meta_box_nonce');

    // Get post meta
    $is_auto_generated = get_post_meta($post->ID, '_abc_auto_generated', true);
    $destination = get_post_meta($post->ID, '_abc_destination', true);
    $keywords = get_post_meta($post->ID, '_abc_keywords', true);
    $content_source = get_post_meta($post->ID, '_abc_content_source', true);

    if ($is_auto_generated) {
        ?>
        <p>
            <strong><?php _e('Auto-Generated:', 'auto-blog-create'); ?></strong> <?php _e('Yes', 'auto-blog-create'); ?>
        </p>
        <p>
            <strong><?php _e('Destination:', 'auto-blog-create'); ?></strong> <?php echo esc_html($destination); ?>
        </p>
        <p>
            <strong><?php _e('Keywords:', 'auto-blog-create'); ?></strong> <?php echo esc_html($keywords); ?>
        </p>
        <?php if ($content_source) : ?>
        <p>
            <strong><?php _e('Content Source:', 'auto-blog-create'); ?></strong> <?php echo esc_html($content_source); ?>
        </p>
        <?php endif; ?>
        <p class="description">
            <?php _e('This post was automatically generated by the Auto Blog Create plugin.', 'auto-blog-create'); ?>
        </p>
        <?php
    } else {
        ?>
        <p>
            <?php _e('This post was not automatically generated.', 'auto-blog-create'); ?>
        </p>
        <p>
            <a href="<?php echo admin_url('admin.php?page=auto-blog-create-generate'); ?>" class="button">
                <?php _e('Generate New Post', 'auto-blog-create'); ?>
            </a>
        </p>
        <?php
    }
}

/**
 * Save meta box data.
 */
function abc_save_meta_box_data($post_id) {
    // Check if nonce is set
    if (!isset($_POST['abc_meta_box_nonce'])) {
        return;
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['abc_meta_box_nonce'], 'abc_meta_box_nonce')) {
        return;
    }

    // If this is an autosave, don't do anything
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Check user permissions
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // No need to save anything here as we're just displaying info
}
add_action('save_post', 'abc_save_meta_box_data');

/**
 * AJAX handler for testing API connections.
 */
function abc_test_api_connection() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'abc_ajax_nonce')) {
        wp_send_json_error(array('message' => __('Security check failed.', 'auto-blog-create')));
    }

    // Check which API to test
    $api = isset($_POST['api']) ? sanitize_text_field($_POST['api']) : '';

    if ($api === 'openrouter') {
        $api_key = get_option('abc_openrouter_api_key');
        if (!$api_key) {
            wp_send_json_error(array('message' => __('OpenRouter API key not configured.', 'ai-auto-blog-creator')));
        }

        // Test OpenRouter API
        $result = abc_test_openrouter_api();

        if ($result['success']) {
            wp_send_json_success(array('message' => __('OpenRouter API connection successful!', 'ai-auto-blog-creator')));
        } else {
            wp_send_json_error(array('message' => $result['message']));
        }
    } elseif ($api === 'huggingface') {
        $model = get_option('abc_huggingface_model');
        if (!$model) {
            wp_send_json_error(array('message' => __('Hugging Face model not configured.', 'auto-blog-create')));
        }

        // Test Hugging Face API
        $result = abc_test_huggingface_api();

        if ($result['success']) {
            wp_send_json_success(array('message' => __('Hugging Face API connection successful!', 'auto-blog-create')));
        } else {
            wp_send_json_error(array('message' => $result['message']));
        }
    } elseif ($api === 'pexels') {
        $api_key = get_option('abc_pexels_api_key');
        if (!$api_key) {
            wp_send_json_error(array('message' => __('Pexels API key not configured.', 'auto-blog-create')));
        }

        // Test Pexels API
        $result = abc_test_pexels_api();

        if ($result['success']) {
            wp_send_json_success(array('message' => __('Pexels API connection successful!', 'auto-blog-create')));
        } else {
            wp_send_json_error(array('message' => $result['message']));
        }
    } else {
        wp_send_json_error(array('message' => __('Invalid API specified.', 'auto-blog-create')));
    }
}
add_action('wp_ajax_abc_test_api_connection', 'abc_test_api_connection');
