# Pexels API Migration Guide

## Overview
This document outlines the complete migration from Unsplash API to Pexels API for the Auto Blog Create WordPress plugin.

## What Changed

### 🔄 API Migration
- **From:** Unsplash API (`https://api.unsplash.com/`)
- **To:** Pexels API (`https://api.pexels.com/v1/`)

### 🔧 Configuration Changes
- **Old Setting:** `abc_unsplash_access_key`
- **New Setting:** `abc_pexels_api_key`

## Files Modified

### 1. `includes/media-handler.php`
- ✅ Replaced `abc_get_unsplash_image()` with `abc_get_pexels_image()`
- ✅ Replaced `abc_test_unsplash_api()` with `abc_test_pexels_api()`
- ✅ Updated image metadata storage for Pexels attribution
- ✅ Added Pexels ID and URL tracking

### 2. `includes/post-creator.php`
- ✅ Updated function call from Unsplash to Pexels
- ✅ Improved attribution caption generation
- ✅ Better source detection for different image providers

### 3. `includes/helpers.php`
- ✅ Updated `abc_is_configured()` to check for Pexels API key

### 4. `auto-blog-create.php`
- ✅ Updated settings registration for Pexels API key
- ✅ Added migration notice for existing users

### 5. `admin/admin-page.php`
- ✅ Updated settings form for Pexels API key
- ✅ Added link to get free Pexels API key
- ✅ Updated status display
- ✅ Added migration notice for existing users

### 6. `admin/admin-functions.php`
- ✅ Updated AJAX handler for Pexels API testing

### 7. `admin/js/admin-script.js`
- ✅ Updated JavaScript for Pexels API test button

## Key Improvements

### 🎯 Better Image Quality
- Pexels provides high-quality, curated images
- Better search relevance for travel content
- Landscape orientation preference for blog headers

### 🔒 Enhanced Reliability
- More stable API with better uptime
- Clearer error handling and messages
- Better rate limiting management

### 📊 Improved Attribution
- Proper photographer credits
- Direct links to Pexels photo pages
- Compliance with Pexels attribution requirements

## Migration Process

### For New Users
1. Get free Pexels API key from https://www.pexels.com/api/
2. Enter API key in plugin settings
3. Test API connection using the built-in test button

### For Existing Users
1. Migration notice will appear in admin
2. Get Pexels API key from https://www.pexels.com/api/
3. Enter new API key in settings
4. Old Unsplash settings will remain but won't be used

## API Differences

### Pexels API Benefits
- ✅ Free with generous limits (200 requests/hour, 20,000/month)
- ✅ No rate limiting issues for typical usage
- ✅ Better image search algorithm
- ✅ Consistent image quality
- ✅ Built-in orientation filtering

### Request Format
```php
// Old Unsplash format
'https://api.unsplash.com/photos/random?query=travel&client_id=KEY'

// New Pexels format  
'https://api.pexels.com/v1/search?query=travel&orientation=landscape'
// With Authorization header: API_KEY
```

## Testing

### Manual Testing
1. Use the included `test-pexels-integration.php` file
2. Test API connection in plugin settings
3. Generate a test blog post

### Automated Testing
- API connection test built into admin interface
- Error logging for debugging issues
- Fallback to Wikimedia Commons if Pexels fails

## Troubleshooting

### Common Issues
1. **Invalid API Key:** Check key format and permissions
2. **No Images Found:** Try broader search terms
3. **Rate Limiting:** Check usage limits in Pexels dashboard

### Debug Information
- Check WordPress error logs
- Use plugin's built-in API test function
- Verify API key has proper permissions

## Security Notes
- API key is stored securely in WordPress options
- All API requests use HTTPS
- Proper input sanitization and validation
- Remember to delete test files after use

## Support
For issues with the migration:
1. Check the migration notice in admin
2. Test API connection using built-in tools
3. Verify API key is correctly entered
4. Check error logs for detailed information
