<?php
/**
 * Test file for Pexels API integration
 * 
 * This file can be used to test the Pexels API integration
 * Place this in your WordPress root and access via browser
 * Remember to delete after testing!
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Include the media handler
require_once('wp-content/plugins/auto blog create/includes/media-handler.php');
require_once('wp-content/plugins/auto blog create/includes/helpers.php');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Pexels API Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .result { background: #f0f0f0; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        img { max-width: 400px; height: auto; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Pexels API Integration Test</h1>
    
    <?php
    // Test 1: Check if API key is configured
    echo "<h2>Test 1: API Key Configuration</h2>";
    $api_key = get_option('abc_pexels_api_key');
    if ($api_key) {
        echo '<div class="result success">✓ Pexels API key is configured</div>';
    } else {
        echo '<div class="result error">✗ Pexels API key is not configured. Please set it in the plugin settings.</div>';
        exit;
    }
    
    // Test 2: Test API connection
    echo "<h2>Test 2: API Connection</h2>";
    $connection_test = abc_test_pexels_api();
    if ($connection_test['success']) {
        echo '<div class="result success">✓ ' . $connection_test['message'] . '</div>';
    } else {
        echo '<div class="result error">✗ ' . $connection_test['message'] . '</div>';
        exit;
    }
    
    // Test 3: Search for travel images
    echo "<h2>Test 3: Image Search</h2>";
    $test_queries = ['Paris travel', 'Tokyo travel', 'Bali travel'];
    
    foreach ($test_queries as $query) {
        echo "<h3>Searching for: $query</h3>";
        $image_data = abc_get_pexels_image($query);
        
        if ($image_data) {
            echo '<div class="result success">';
            echo "✓ Found image by: " . $image_data['author'] . "<br>";
            echo "Alt text: " . $image_data['alt_text'] . "<br>";
            echo "Download URL: " . $image_data['download_url'] . "<br>";
            echo "Author URL: " . $image_data['author_url'] . "<br>";
            if (isset($image_data['pexels_id'])) {
                echo "Pexels ID: " . $image_data['pexels_id'] . "<br>";
            }
            echo '<img src="' . $image_data['url'] . '" alt="' . $image_data['alt_text'] . '">';
            echo '</div>';
        } else {
            echo '<div class="result error">✗ No image found for query: ' . $query . '</div>';
        }
    }
    
    // Test 4: Check plugin configuration
    echo "<h2>Test 4: Plugin Configuration</h2>";
    if (abc_is_configured()) {
        echo '<div class="result success">✓ Plugin is fully configured</div>';
    } else {
        echo '<div class="result error">✗ Plugin is not fully configured</div>';
    }
    ?>
    
    <h2>Summary</h2>
    <p>If all tests pass, your Pexels API integration is working correctly!</p>
    <p><strong>Remember to delete this test file after testing for security reasons.</strong></p>
    
</body>
</html>
