<?php
/**
 * Debug tool for Pexels API integration issues
 * 
 * This file helps diagnose why images aren't showing in blog posts
 * Place this in your WordPress root and access via browser
 * Remember to delete after debugging!
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Include the plugin files
require_once('wp-content/plugins/auto blog create/includes/media-handler.php');
require_once('wp-content/plugins/auto blog create/includes/helpers.php');
require_once('wp-content/plugins/auto blog create/includes/content-generator.php');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Pexels API Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .result { background: #f0f0f0; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        img { max-width: 400px; height: auto; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .debug-section { margin: 30px 0; border: 1px solid #ddd; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Pexels API Debug Tool</h1>
    
    <?php
    // Enable WordPress debug mode temporarily
    $original_debug = defined('WP_DEBUG') ? WP_DEBUG : false;
    if (!defined('WP_DEBUG')) {
        define('WP_DEBUG', true);
    }
    
    echo '<div class="debug-section">';
    echo '<h2>Step 1: Configuration Check</h2>';
    
    // Check API key
    $api_key = get_option('abc_pexels_api_key');
    if ($api_key) {
        echo '<div class="result success">✓ Pexels API key is configured: ' . substr($api_key, 0, 10) . '...</div>';
    } else {
        echo '<div class="result error">✗ Pexels API key is not configured!</div>';
        echo '<p>Please go to Auto Blog Create → Settings and add your Pexels API key.</p>';
        exit;
    }
    
    // Check WordPress debug
    echo '<div class="result info">WordPress Debug Mode: ' . (WP_DEBUG ? 'Enabled' : 'Disabled') . '</div>';
    echo '<div class="result info">Error Log Location: ' . ini_get('error_log') . '</div>';
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 2: API Connection Test</h2>';
    
    $connection_test = abc_test_pexels_api();
    if ($connection_test['success']) {
        echo '<div class="result success">✓ ' . $connection_test['message'] . '</div>';
    } else {
        echo '<div class="result error">✗ ' . $connection_test['message'] . '</div>';
        exit;
    }
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 3: Image Search Test</h2>';
    
    $test_query = 'Paris travel';
    echo "<h3>Testing search for: $test_query</h3>";
    
    // Capture error logs
    ob_start();
    $image_data = abc_get_pexels_image($test_query);
    $output = ob_get_clean();
    
    if ($image_data) {
        echo '<div class="result success">✓ Image found successfully!</div>';
        echo '<pre>' . print_r($image_data, true) . '</pre>';
        echo '<img src="' . $image_data['url'] . '" alt="' . $image_data['alt_text'] . '">';
    } else {
        echo '<div class="result error">✗ No image found</div>';
    }
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 4: Image Download Test</h2>';
    
    if ($image_data) {
        // Test image download without attaching to post
        echo '<h3>Testing image download capability</h3>';
        
        $test_url = $image_data['download_url'];
        echo '<div class="result info">Testing URL: ' . $test_url . '</div>';
        
        $response = wp_remote_get($test_url, array('timeout' => 30));
        
        if (is_wp_error($response)) {
            echo '<div class="result error">✗ Failed to download image: ' . $response->get_error_message() . '</div>';
        } else {
            $response_code = wp_remote_retrieve_response_code($response);
            $content_type = wp_remote_retrieve_header($response, 'content-type');
            $content_length = wp_remote_retrieve_header($response, 'content-length');
            
            echo '<div class="result success">✓ Image download successful</div>';
            echo '<div class="result info">Response Code: ' . $response_code . '</div>';
            echo '<div class="result info">Content Type: ' . $content_type . '</div>';
            echo '<div class="result info">Content Length: ' . $content_length . ' bytes</div>';
        }
    }
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 5: WordPress Media Functions Test</h2>';
    
    // Check if required functions exist
    $required_functions = ['media_sideload_image', 'set_post_thumbnail', 'wp_insert_post'];
    foreach ($required_functions as $func) {
        if (function_exists($func)) {
            echo '<div class="result success">✓ Function ' . $func . ' exists</div>';
        } else {
            echo '<div class="result error">✗ Function ' . $func . ' missing</div>';
        }
    }
    
    // Check upload directory
    $upload_dir = wp_upload_dir();
    if ($upload_dir['error']) {
        echo '<div class="result error">✗ Upload directory error: ' . $upload_dir['error'] . '</div>';
    } else {
        echo '<div class="result success">✓ Upload directory: ' . $upload_dir['path'] . '</div>';
        echo '<div class="result info">Upload URL: ' . $upload_dir['url'] . '</div>';
        
        // Check if directory is writable
        if (is_writable($upload_dir['path'])) {
            echo '<div class="result success">✓ Upload directory is writable</div>';
        } else {
            echo '<div class="result error">✗ Upload directory is not writable</div>';
        }
    }
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 6: Recent Error Logs</h2>';
    
    // Try to read recent error logs
    $error_log_file = ini_get('error_log');
    if ($error_log_file && file_exists($error_log_file)) {
        $log_content = file_get_contents($error_log_file);
        $lines = explode("\n", $log_content);
        $recent_lines = array_slice($lines, -50); // Last 50 lines
        
        $abc_errors = array_filter($recent_lines, function($line) {
            return strpos($line, 'Auto Blog Create') !== false;
        });
        
        if (!empty($abc_errors)) {
            echo '<div class="result warning">Recent Auto Blog Create errors:</div>';
            echo '<pre>' . implode("\n", $abc_errors) . '</pre>';
        } else {
            echo '<div class="result info">No recent Auto Blog Create errors found in logs</div>';
        }
    } else {
        echo '<div class="result warning">Error log file not accessible</div>';
    }
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 7: Recommendations</h2>';
    
    echo '<div class="result info">';
    echo '<h3>To fix image issues:</h3>';
    echo '<ol>';
    echo '<li>Check that your Pexels API key is valid and has proper permissions</li>';
    echo '<li>Ensure WordPress upload directory is writable</li>';
    echo '<li>Enable WordPress debug mode in wp-config.php: <code>define("WP_DEBUG", true);</code></li>';
    echo '<li>Check error logs after generating a post</li>';
    echo '<li>Try generating a test post and check if featured image is set</li>';
    echo '<li>Verify your server can download external images (check firewall/security plugins)</li>';
    echo '</ol>';
    echo '</div>';
    echo '</div>';
    ?>
    
    <h2>Next Steps</h2>
    <p><strong>1. Generate a test post:</strong> Go to Auto Blog Create → Generate Post and create a test post with a simple destination like "Paris"</p>
    <p><strong>2. Check the post:</strong> Look at the generated post to see if it has a featured image</p>
    <p><strong>3. Check error logs:</strong> Look for any new errors in your WordPress error log</p>
    <p><strong>4. Delete this file:</strong> Remove this debug file for security</p>
    
</body>
</html>
