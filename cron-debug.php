<?php
/**
 * WordPress Cron Debug Tool for Auto Blog Creator
 *
 * This file helps diagnose why automatic blog generation isn't working
 * Access via: /wp-content/plugins/Auto Travel Blog creator/cron-debug.php
 * Remember to delete after debugging!
 */

// Load WordPress from plugin directory
$wp_root = dirname(dirname(dirname(dirname(__FILE__))));
require_once($wp_root . '/wp-config.php');
require_once($wp_root . '/wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied. You must be an administrator to use this tool.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Auto Blog Creator - Cron Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { padding: 10px; margin: 10px 0; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .button { background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
        .button:hover { background: #005a87; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Auto Blog Creator - Cron Debug Tool</h1>
    
    <?php
    echo '<div class="debug-section">';
    echo '<h2>Step 1: WordPress Cron System Check</h2>';
    
    // Check if WP Cron is disabled
    if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
        echo '<div class="result error">✗ WordPress Cron is DISABLED in wp-config.php</div>';
        echo '<p>WordPress cron is disabled. You need to set up a real cron job on your server or enable WP Cron.</p>';
        echo '<p>To enable WP Cron, remove or comment out this line in wp-config.php: <code>define("DISABLE_WP_CRON", true);</code></p>';
    } else {
        echo '<div class="result success">✓ WordPress Cron is enabled</div>';
    }
    
    // Check if the plugin's cron job is scheduled
    $next_scheduled = wp_next_scheduled('abc_scheduled_post_creation');
    if ($next_scheduled) {
        echo '<div class="result success">✓ Auto Blog Creator cron job is scheduled</div>';
        echo '<p>Next run: ' . date('Y-m-d H:i:s', $next_scheduled) . ' (in ' . human_time_diff($next_scheduled) . ')</p>';
    } else {
        echo '<div class="result error">✗ Auto Blog Creator cron job is NOT scheduled</div>';
    }
    
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 2: Plugin Configuration Check</h2>';
    
    // Check if required functions exist
    if (function_exists('abc_generate_travel_post')) {
        echo '<div class="result success">✓ Travel post generation function exists</div>';
    } else {
        echo '<div class="result error">✗ Travel post generation function missing</div>';
    }
    
    if (function_exists('abc_generate_mental_health_post')) {
        echo '<div class="result success">✓ Mental health post generation function exists</div>';
    } else {
        echo '<div class="result error">✗ Mental health post generation function missing</div>';
    }
    
    // Check API configuration
    $openrouter_key = get_option('abc_openrouter_api_key');
    if (!empty($openrouter_key)) {
        echo '<div class="result success">✓ OpenRouter API key is configured</div>';
    } else {
        echo '<div class="result warning">⚠ OpenRouter API key is not configured</div>';
    }
    
    $pexels_key = get_option('abc_pexels_api_key');
    if (!empty($pexels_key)) {
        echo '<div class="result success">✓ Pexels API key is configured</div>';
    } else {
        echo '<div class="result warning">⚠ Pexels API key is not configured</div>';
    }
    
    // Check Wikipedia fallback
    $use_wikipedia = get_option('abc_use_wikipedia');
    if ($use_wikipedia) {
        echo '<div class="result info">ℹ Wikipedia content source is enabled (API not required)</div>';
    }
    
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 3: Post Generation Settings</h2>';
    
    $travel_count = get_option('abc_travel_posts_per_day', 5);
    $mental_health_count = get_option('abc_mental_health_posts_per_day', 5);
    $frequency = get_option('abc_post_frequency', 'daily');
    $human_review = get_option('abc_human_review', 1);
    
    echo '<table>';
    echo '<tr><th>Setting</th><th>Value</th></tr>';
    echo '<tr><td>Travel posts per day</td><td>' . $travel_count . '</td></tr>';
    echo '<tr><td>Mental health posts per day</td><td>' . $mental_health_count . '</td></tr>';
    echo '<tr><td>Post frequency</td><td>' . $frequency . '</td></tr>';
    echo '<tr><td>Human review required</td><td>' . ($human_review ? 'Yes (posts saved as drafts)' : 'No (posts published automatically)') . '</td></tr>';
    echo '</table>';
    
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 4: Recent Auto-Generated Posts</h2>';
    
    $recent_posts = get_posts(array(
        'meta_key' => '_abc_auto_generated',
        'meta_value' => '1',
        'posts_per_page' => 10,
        'post_status' => array('publish', 'draft'),
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    if (!empty($recent_posts)) {
        echo '<div class="result success">✓ Found ' . count($recent_posts) . ' recent auto-generated posts</div>';
        echo '<table>';
        echo '<tr><th>Title</th><th>Status</th><th>Date</th><th>Type</th></tr>';
        foreach ($recent_posts as $post) {
            $content_type = get_post_meta($post->ID, '_abc_content_type', true);
            echo '<tr>';
            echo '<td><a href="' . get_edit_post_link($post->ID) . '">' . $post->post_title . '</a></td>';
            echo '<td>' . $post->post_status . '</td>';
            echo '<td>' . $post->post_date . '</td>';
            echo '<td>' . ($content_type ?: 'Unknown') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<div class="result warning">⚠ No auto-generated posts found</div>';
        echo '<p>This could mean:</p>';
        echo '<ul>';
        echo '<li>The cron job has never run successfully</li>';
        echo '<li>Posts are being generated but not marked as auto-generated</li>';
        echo '<li>There are errors preventing post creation</li>';
        echo '</ul>';
    }
    
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 5: WordPress Cron Events</h2>';
    
    $cron_events = _get_cron_array();
    $abc_events = array();
    
    foreach ($cron_events as $timestamp => $events) {
        foreach ($events as $hook => $event_data) {
            if (strpos($hook, 'abc_') === 0) {
                $abc_events[] = array(
                    'hook' => $hook,
                    'timestamp' => $timestamp,
                    'time' => date('Y-m-d H:i:s', $timestamp),
                    'args' => $event_data
                );
            }
        }
    }
    
    if (!empty($abc_events)) {
        echo '<div class="result success">✓ Found Auto Blog Creator cron events</div>';
        echo '<table>';
        echo '<tr><th>Hook</th><th>Scheduled Time</th><th>Status</th></tr>';
        foreach ($abc_events as $event) {
            $status = $event['timestamp'] > time() ? 'Pending' : 'Overdue';
            echo '<tr>';
            echo '<td>' . $event['hook'] . '</td>';
            echo '<td>' . $event['time'] . '</td>';
            echo '<td>' . $status . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<div class="result error">✗ No Auto Blog Creator cron events found</div>';
    }
    
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 6: Error Log Check</h2>';
    
    // Try to read recent error logs
    $error_log_file = ini_get('error_log');
    if ($error_log_file && file_exists($error_log_file)) {
        $log_content = file_get_contents($error_log_file);
        $lines = explode("\n", $log_content);
        $recent_lines = array_slice($lines, -100); // Last 100 lines
        
        $abc_errors = array_filter($recent_lines, function($line) {
            return strpos($line, 'AI Auto Blog Creator') !== false || strpos($line, 'Auto Blog Create') !== false;
        });
        
        if (!empty($abc_errors)) {
            echo '<div class="result warning">⚠ Recent Auto Blog Creator log entries found:</div>';
            echo '<pre>' . implode("\n", array_slice($abc_errors, -10)) . '</pre>';
        } else {
            echo '<div class="result info">ℹ No recent Auto Blog Creator errors found in logs</div>';
        }
    } else {
        echo '<div class="result warning">⚠ Error log file not accessible</div>';
        echo '<p>Error log location: ' . ($error_log_file ?: 'Not configured') . '</p>';
    }
    
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 7: Quick Actions</h2>';
    
    echo '<p>Use these buttons to test and fix common issues:</p>';
    
    if (isset($_GET['action'])) {
        switch ($_GET['action']) {
            case 'reschedule':
                wp_clear_scheduled_hook('abc_scheduled_post_creation');
                $frequency = get_option('abc_post_frequency', 'daily');
                $result = wp_schedule_event(time(), $frequency, 'abc_scheduled_post_creation');
                if ($result !== false) {
                    echo '<div class="result success">✓ Cron job rescheduled successfully</div>';
                } else {
                    echo '<div class="result error">✗ Failed to reschedule cron job</div>';
                }
                break;
                
            case 'manual_trigger':
                if (function_exists('abc_create_scheduled_post')) {
                    echo '<div class="result info">ℹ Triggering manual post creation...</div>';
                    abc_create_scheduled_post();
                    echo '<div class="result success">✓ Manual trigger completed. Check the posts and error logs.</div>';
                } else {
                    echo '<div class="result error">✗ Scheduled post function not found</div>';
                }
                break;
                
            case 'test_single':
                if (function_exists('abc_generate_travel_post')) {
                    echo '<div class="result info">ℹ Generating a single test travel post...</div>';
                    $post_id = abc_generate_travel_post('Paris');
                    if ($post_id) {
                        echo '<div class="result success">✓ Test post created successfully. Post ID: ' . $post_id . '</div>';
                        echo '<p><a href="' . get_edit_post_link($post_id) . '">View the test post</a></p>';
                    } else {
                        echo '<div class="result error">✗ Failed to create test post</div>';
                    }
                } else {
                    echo '<div class="result error">✗ Travel post generation function not found</div>';
                }
                break;
        }
        echo '<hr>';
    }
    
    echo '<a href="?action=reschedule" class="button">Reschedule Cron Job</a>';
    echo '<a href="?action=manual_trigger" class="button">Manual Trigger (Test)</a>';
    echo '<a href="?action=test_single" class="button">Generate Single Test Post</a>';
    echo '<a href="?" class="button">Refresh Status</a>';
    
    echo '</div>';
    
    echo '<div class="debug-section">';
    echo '<h2>Step 8: Recommendations</h2>';
    
    echo '<div class="result info">';
    echo '<h3>Common Solutions:</h3>';
    echo '<ol>';
    echo '<li><strong>If cron is not scheduled:</strong> Click "Reschedule Cron Job" above</li>';
    echo '<li><strong>If WordPress Cron is disabled:</strong> Enable it in wp-config.php or set up a real server cron job</li>';
    echo '<li><strong>If API keys are missing:</strong> Configure them in Auto Blog Creator → Settings</li>';
    echo '<li><strong>If posts aren\'t appearing:</strong> Check if "Human Review" is enabled (posts saved as drafts)</li>';
    echo '<li><strong>If there are errors:</strong> Check the error logs and ensure all plugin files are uploaded correctly</li>';
    echo '<li><strong>For hosting with disabled WP Cron:</strong> Set up a server cron job to call wp-cron.php every few minutes</li>';
    echo '</ol>';
    echo '</div>';
    echo '</div>';
    ?>
    
    <h2>Next Steps</h2>
    <p><strong>1. Fix any red errors above</strong></p>
    <p><strong>2. Test with "Manual Trigger" or "Generate Single Test Post"</strong></p>
    <p><strong>3. Check your WordPress posts for new content</strong></p>
    <p><strong>4. Delete this debug file for security</strong></p>
    
</body>
</html>
