<?php
/**
 * Emergency Fix for Auto Blog Create Plugin
 * 
 * This file provides temporary functions to prevent critical errors
 * while the main plugin files are being updated.
 * 
 * INSTRUCTIONS:
 * 1. Upload this file to your WordPress root directory
 * 2. Access it via: yoursite.com/emergency-fix.php
 * 3. This will create backup functions and fix the critical error
 * 4. Delete this file after the issue is resolved
 */

// Load WordPress
if (file_exists('wp-config.php')) {
    require_once('wp-config.php');
} else {
    die('WordPress not found. Make sure this file is in your WordPress root directory.');
}

if (file_exists('wp-load.php')) {
    require_once('wp-load.php');
} else {
    die('WordPress not found. Make sure this file is in your WordPress root directory.');
}

// Check if we're running this script
if (!isset($_GET['run_fix'])) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Auto Blog Create - Emergency Fix</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
            .button { background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <h1>Auto Blog Create - Emergency Fix</h1>
        
        <div class="warning">
            <strong>Warning:</strong> This script will attempt to fix critical errors in the Auto Blog Create plugin.
            Make sure you have a backup of your website before proceeding.
        </div>
        
        <h2>What this fix does:</h2>
        <ul>
            <li>Creates backup functions to prevent fatal errors</li>
            <li>Temporarily disables problematic features</li>
            <li>Allows you to access your WordPress admin again</li>
            <li>Provides a safe way to test the plugin functionality</li>
        </ul>
        
        <h2>Steps to fix:</h2>
        <ol>
            <li>Click the "Run Emergency Fix" button below</li>
            <li>Check if your website loads properly</li>
            <li>Go to WordPress Admin → Plugins</li>
            <li>Deactivate and reactivate the Auto Blog Create plugin</li>
            <li>Delete this emergency-fix.php file</li>
        </ol>
        
        <p>
            <a href="?run_fix=1" class="button">Run Emergency Fix</a>
        </p>
        
        <p><strong>Note:</strong> If this doesn't work, you may need to deactivate the plugin via FTP by renaming the plugin folder.</p>
    </body>
    </html>
    <?php
    exit;
}

// Run the emergency fix
echo "<h1>Running Emergency Fix...</h1>";

// Create backup functions if they don't exist
if (!function_exists('abc_generate_fitness_post')) {
    function abc_generate_fitness_post($topic, $keywords) {
        error_log("Auto Blog Create: Emergency function abc_generate_fitness_post called with topic: $topic");
        return false;
    }
    echo "<p>✓ Created backup abc_generate_fitness_post function</p>";
}

if (!function_exists('abc_generate_fitness_content')) {
    function abc_generate_fitness_content($topic, $section = '') {
        error_log("Auto Blog Create: Emergency function abc_generate_fitness_content called");
        return "This is placeholder content for $topic. The plugin is currently being updated.";
    }
    echo "<p>✓ Created backup abc_generate_fitness_content function</p>";
}

if (!function_exists('abc_fitness_page')) {
    function abc_fitness_page() {
        echo '<div class="wrap"><h1>Fitness Posts</h1><p>The fitness functionality is currently being updated. Please check back soon.</p></div>';
    }
    echo "<p>✓ Created backup abc_fitness_page function</p>";
}

if (!function_exists('abc_get_fitness_category_id')) {
    function abc_get_fitness_category_id() {
        // Try to get fitness category, fallback to default
        $category = get_term_by('name', 'Fitness', 'category');
        if ($category) {
            return $category->term_id;
        }
        return get_option('default_category', 1);
    }
    echo "<p>✓ Created backup abc_get_fitness_category_id function</p>";
}

// Clear any scheduled events that might be causing issues
wp_clear_scheduled_hook('abc_scheduled_post_creation');
echo "<p>✓ Cleared scheduled events</p>";

// Flush rewrite rules
flush_rewrite_rules();
echo "<p>✓ Flushed rewrite rules</p>";

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h2>Emergency Fix Complete!</h2>";
echo "<p>The emergency fix has been applied. Your website should now be accessible.</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Go to your <a href='" . admin_url() . "'>WordPress Admin</a></li>";
echo "<li>Check if everything is working</li>";
echo "<li>Go to Plugins and deactivate/reactivate Auto Blog Create if needed</li>";
echo "<li>Delete this emergency-fix.php file for security</li>";
echo "</ol>";
echo "</div>";

echo "<p><a href='" . admin_url() . "' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>Go to WordPress Admin</a></p>";

echo "<h3>Troubleshooting Tips:</h3>";
echo "<ul>";
echo "<li>If you still see errors, try deactivating the Auto Blog Create plugin</li>";
echo "<li>Check your error logs for specific error messages</li>";
echo "<li>Make sure all plugin files were uploaded correctly</li>";
echo "<li>Try switching to a default WordPress theme temporarily</li>";
echo "</ul>";
?>
