<?php
/**
 * SEO optimizer functions for Auto Blog Create plugin.
 *
 * @package Auto_Blog_Create
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Generate an SEO-friendly title for a blog post.
 *
 * @param string $topic The main topic.
 * @param string $keywords Comma-separated keywords.
 * @param string $content_type The content type ('travel', 'mental_health', or 'fitness').
 * @return string The generated title.
 */
function abc_generate_seo_title($topic, $keywords, $content_type = 'fitness') {
    // Get templates based on content type
    if ($content_type === 'travel') {
        $templates = abc_get_travel_title_templates();
    } elseif ($content_type === 'mental_health') {
        $templates = abc_get_mental_health_title_templates();
    } else {
        $templates = abc_get_fitness_title_templates();
    }

    // Select a random template
    $template = $templates[array_rand($templates)];

    // Get a random keyword from the list
    $keywords_array = explode(',', $keywords);
    $keyword = trim($keywords_array[array_rand($keywords_array)]);

    // Format the title
    $title = sprintf($template, $topic, $keyword);

    return $title;
}

/**
 * Get travel title templates.
 *
 * @return array Array of title templates.
 */
function abc_get_travel_title_templates() {
    return array(
        "Ultimate %s Travel Guide: %s Tips for Your Perfect Trip",
        "Discover %s: Complete %s Travel Guide",
        "Your %s Adventure: Essential %s Travel Tips",
        "%s Travel Guide: Best %s Experiences",
        "Exploring %s: Your Complete %s Travel Companion",
        "%s Destination Guide: %s Travel Essentials",
        "Visit %s: Ultimate %s Travel Planning Guide",
        "%s Travel Tips: Your %s Adventure Awaits",
        "The Complete %s Guide: %s Travel Secrets",
        "%s Journey: Essential %s Travel Information"
    );
}

/**
 * Get mental health title templates.
 *
 * @return array Array of title templates.
 */
function abc_get_mental_health_title_templates() {
    return array(
        "Understanding %s: Your %s Wellness Guide",
        "%s Support: Essential %s Strategies",
        "Managing %s: Complete %s Wellness Guide",
        "%s Wellness: Your %s Mental Health Journey",
        "Coping with %s: %s Support and Strategies",
        "%s Guide: %s Mental Health Resources",
        "Your %s Journey: %s Wellness Tips",
        "%s Support: Complete %s Mental Health Guide",
        "Navigating %s: %s Wellness Strategies",
        "%s Wellness: Your %s Mental Health Companion"
    );
}

/**
 * Get fitness title templates.
 *
 * @return array Array of title templates.
 */
function abc_get_fitness_title_templates() {
    return array(
        "Ultimate %s Guide: %s You Need to Know",
        "Top 10 %s Tips: Complete %s Guide",
        "Mastering %s: The Complete %s Fitness Guide",
        "%s Guide: %s Tips and Techniques",
        "Transform Your Body with %s: %s Strategies",
        "%s Workout Guide: Best %s Exercises",
        "The Essential %s Guide: %s Tips for Success",
        "Getting Started with %s: %s Beginner's Guide",
        "%s for Beginners: Your Complete %s Guide",
        "Effective %s: Your %s Transformation Guide"
    );
}

/**
 * Generate an SEO-friendly meta description.
 *
 * @param string $topic The main topic.
 * @param string $content The post content.
 * @param string $content_type The content type ('travel', 'mental_health', or 'fitness').
 * @return string The generated meta description.
 */
function abc_generate_meta_description($topic, $content, $content_type = 'fitness') {
    // Extract the first 120 characters of content
    $excerpt = wp_strip_all_tags($content);
    $excerpt = substr($excerpt, 0, 120);

    // Generate description based on content type
    if ($content_type === 'travel') {
        $description = "Discover $topic with our comprehensive travel guide. $excerpt...";
    } elseif ($content_type === 'mental_health') {
        $description = "Learn about $topic with our supportive mental health guide. $excerpt...";
    } else {
        $description = "Master $topic with our comprehensive fitness guide. $excerpt...";
    }

    // Ensure it's not too long (max 160 characters for meta description)
    if (strlen($description) > 160) {
        $description = substr($description, 0, 157) . '...';
    }

    return $description;
}

/**
 * Insert keywords into content.
 *
 * @param string $content The post content.
 * @param string $fitness_topic The fitness topic.
 * @param string $keywords Comma-separated keywords.
 * @return string The optimized content.
 */
function abc_insert_keywords($content, $fitness_topic, $keywords) {
    // Convert keywords string to array
    $keywords_array = array_map('trim', explode(',', $keywords));

    // Create fitness-specific long-tail keywords
    $long_tail_keywords = array();
    foreach ($keywords_array as $keyword) {
        $long_tail_keywords[] = "best $keyword for $fitness_topic";
        $long_tail_keywords[] = "$fitness_topic $keyword guide";
        $long_tail_keywords[] = "effective $keyword for $fitness_topic";
        $long_tail_keywords[] = "$fitness_topic $keyword tips";
    }
    
    // Split content into paragraphs
    $paragraphs = explode("\n\n", $content);
    
    // Insert keywords into first and last paragraphs if they exist
    if (count($paragraphs) > 0) {
        // First paragraph
        $first_keyword = $long_tail_keywords[array_rand($long_tail_keywords)];
        $paragraphs[0] = str_replace($fitness_topic, "<strong>$fitness_topic</strong>", $paragraphs[0]);
        $paragraphs[0] .= " This guide provides essential information for fitness enthusiasts looking for $first_keyword.";

        // Last paragraph
        if (count($paragraphs) > 1) {
            $last_index = count($paragraphs) - 1;
            $last_keyword = $long_tail_keywords[array_rand($long_tail_keywords)];
            $paragraphs[$last_index] .= " For more information about $last_keyword, consult with certified fitness professionals.";
        }
    }
    
    // Rejoin paragraphs
    $optimized_content = implode("\n\n", $paragraphs);
    
    return $optimized_content;
}

/**
 * Add internal links to related content.
 *
 * @param string $content The post content.
 * @param string $fitness_topic The fitness topic.
 * @return string The content with internal links.
 */
function abc_add_internal_links($content, $fitness_topic) {
    // Find related posts
    $related_args = array(
        'post_type'      => 'post',
        'posts_per_page' => 3,
        'post_status'    => 'publish',
        's'              => $fitness_topic,
        'meta_query'     => array(
            array(
                'key'     => '_abc_auto_generated',
                'value'   => '1',
                'compare' => '=',
            ),
        ),
    );

    $related_query = new WP_Query($related_args);

    if ($related_query->have_posts()) {
        // Add a "Related Posts" section
        $content .= "\n\n<h2>Related Fitness Guides</h2>\n<ul>";

        while ($related_query->have_posts()) {
            $related_query->the_post();
            $content .= "\n<li><a href=\"" . get_permalink() . "\">" . get_the_title() . "</a></li>";
        }

        $content .= "\n</ul>";
        wp_reset_postdata();
    }

    return $content;
}

/**
 * Generate structured data for fitness article.
 *
 * @param string $title The post title.
 * @param string $fitness_topic The fitness topic.
 * @param string $permalink The post permalink.
 * @param string $featured_image_url The URL of the featured image.
 * @return string The structured data JSON-LD script.
 */
function abc_generate_structured_data($title, $fitness_topic, $permalink, $featured_image_url) {
    $structured_data = array(
        '@context'      => 'https://schema.org',
        '@type'         => 'Article',
        'headline'      => $title,
        'description'   => "A comprehensive fitness guide about $fitness_topic",
        'url'           => $permalink,
        'mainEntityOfPage' => array(
            '@type' => 'WebPage',
            '@id'   => $permalink,
        ),
        'articleSection' => 'Fitness',
        'keywords'      => $fitness_topic . ', fitness, workout, health, exercise',
    );

    if ($featured_image_url) {
        $structured_data['image'] = $featured_image_url;
    }

    $json = wp_json_encode($structured_data);

    return '<script type="application/ld+json">' . $json . '</script>';
}

/**
 * Check if Yoast SEO plugin is active.
 *
 * @return bool True if Yoast SEO is active, false otherwise.
 */
function abc_is_yoast_active() {
    return defined('WPSEO_VERSION');
}

/**
 * Set Yoast SEO meta data for a post.
 *
 * @param int $post_id The post ID.
 * @param string $focus_keyword The focus keyword.
 * @param string $meta_description The meta description.
 */
function abc_set_yoast_metadata($post_id, $focus_keyword, $meta_description) {
    if (!abc_is_yoast_active()) {
        return;
    }
    
    // Set focus keyword
    update_post_meta($post_id, '_yoast_wpseo_focuskw', $focus_keyword);
    
    // Set meta description
    update_post_meta($post_id, '_yoast_wpseo_metadesc', $meta_description);
}
