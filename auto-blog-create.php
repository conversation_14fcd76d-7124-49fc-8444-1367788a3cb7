<?php
/**
 * Plugin Name: AI Auto Blog Creator
 * Plugin URI: https://example.com/ai-auto-blog-creator
 * Description: Automatically generates 10 daily SEO-optimized blog posts (5 travel, 5 mental health) using Deepseek R1 API from OpenRouter with Wikipedia integration.
 * Version: 2.0.0
 * Author: WordPress Developer
 * Author URI: https://example.com
 * Text Domain: ai-auto-blog-creator
 * Domain Path: /languages
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Increase execution time limit
set_time_limit(300); // 5 minutes
ini_set('max_execution_time', 300);

// Define plugin constants
define('ABC_VERSION', '2.0.0');
define('ABC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ABC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ABC_ADMIN_DIR', ABC_PLUGIN_DIR . 'admin/');
define('ABC_INCLUDES_DIR', ABC_PLUGIN_DIR . 'includes/');
define('ABC_ASSETS_URL', ABC_PLUGIN_URL . 'assets/');

// Include required files - with error handling
if (file_exists(ABC_INCLUDES_DIR . 'content-generator.php')) {
    require_once ABC_INCLUDES_DIR . 'content-generator.php';
}
if (file_exists(ABC_INCLUDES_DIR . 'seo-optimizer.php')) {
    require_once ABC_INCLUDES_DIR . 'seo-optimizer.php';
}
if (file_exists(ABC_INCLUDES_DIR . 'media-handler.php')) {
    require_once ABC_INCLUDES_DIR . 'media-handler.php';
}
if (file_exists(ABC_INCLUDES_DIR . 'post-creator.php')) {
    require_once ABC_INCLUDES_DIR . 'post-creator.php';
}
if (file_exists(ABC_INCLUDES_DIR . 'helpers.php')) {
    require_once ABC_INCLUDES_DIR . 'helpers.php';
}
if (file_exists(ABC_ADMIN_DIR . 'admin-functions.php')) {
    require_once ABC_ADMIN_DIR . 'admin-functions.php';
}

/**
 * The code that runs during plugin activation.
 */
function activate_auto_blog_create() {
    // Create custom database tables if needed
    // Set up initial options

    // Set default options if they don't exist
    if (!get_option('abc_openrouter_api_key')) {
        update_option('abc_openrouter_api_key', '');
    }

    if (!get_option('abc_deepseek_model')) {
        update_option('abc_deepseek_model', 'deepseek/deepseek-r1');
    }

    if (!get_option('abc_post_frequency')) {
        update_option('abc_post_frequency', 'daily');
    }

    if (!get_option('abc_daily_post_count')) {
        update_option('abc_daily_post_count', 10);
    }

    if (!get_option('abc_travel_posts_per_day')) {
        update_option('abc_travel_posts_per_day', 5);
    }

    if (!get_option('abc_mental_health_posts_per_day')) {
        update_option('abc_mental_health_posts_per_day', 5);
    }

    // Migration: Check if user has Unsplash key and show migration notice
    if (get_option('abc_unsplash_access_key') && !get_option('abc_pexels_api_key')) {
        update_option('abc_migration_notice', 1);
    }

    // Schedule the cron job for auto-posting (daily)
    if (!wp_next_scheduled('abc_scheduled_post_creation')) {
        wp_schedule_event(time(), 'daily', 'abc_scheduled_post_creation');
    }

    // Flush rewrite rules to ensure fitness page works
    flush_rewrite_rules();
}

/**
 * The code that runs during plugin deactivation.
 */
function deactivate_auto_blog_create() {
    // Clear scheduled hooks
    wp_clear_scheduled_hook('abc_scheduled_post_creation');
}

register_activation_hook(__FILE__, 'activate_auto_blog_create');
register_deactivation_hook(__FILE__, 'deactivate_auto_blog_create');

/**
 * Initialize the plugin.
 */
function abc_init() {
    // Load plugin text domain for internationalization
    load_plugin_textdomain('ai-auto-blog-creator', false, dirname(plugin_basename(__FILE__)) . '/languages');
}
add_action('plugins_loaded', 'abc_init');

/**
 * Enqueue styles and scripts.
 */
function abc_enqueue_assets() {
    // Enqueue admin styles
    if (is_admin()) {
        wp_enqueue_style('abc-admin-style', ABC_ASSETS_URL . 'css/fitness-page.css', array(), ABC_VERSION);
    }

    // Enqueue frontend styles for fitness page
    if (get_query_var('abc_fitness_page')) {
        wp_enqueue_style('abc-fitness-style', ABC_ASSETS_URL . 'css/fitness-page.css', array(), ABC_VERSION);
    }
}
add_action('wp_enqueue_scripts', 'abc_enqueue_assets');
add_action('admin_enqueue_scripts', 'abc_enqueue_assets');

/**
 * Register admin menu.
 */
function abc_admin_menu() {
    add_menu_page(
        __('AI Auto Blog Creator', 'ai-auto-blog-creator'),
        __('AI Auto Blog Creator', 'ai-auto-blog-creator'),
        'manage_options',
        'ai-auto-blog-creator',
        'abc_admin_page',
        'dashicons-edit',
        30
    );

    add_submenu_page(
        'ai-auto-blog-creator',
        __('Settings', 'ai-auto-blog-creator'),
        __('Settings', 'ai-auto-blog-creator'),
        'manage_options',
        'ai-auto-blog-creator-settings',
        'abc_settings_page'
    );

    add_submenu_page(
        'ai-auto-blog-creator',
        __('Generate Posts', 'ai-auto-blog-creator'),
        __('Generate Posts', 'ai-auto-blog-creator'),
        'manage_options',
        'ai-auto-blog-creator-generate',
        'abc_generate_page'
    );

    add_submenu_page(
        'ai-auto-blog-creator',
        __('Travel Posts', 'ai-auto-blog-creator'),
        __('Travel Posts', 'ai-auto-blog-creator'),
        'manage_options',
        'ai-auto-blog-creator-travel',
        'abc_travel_page'
    );

    add_submenu_page(
        'ai-auto-blog-creator',
        __('Mental Health Posts', 'ai-auto-blog-creator'),
        __('Mental Health Posts', 'ai-auto-blog-creator'),
        'manage_options',
        'ai-auto-blog-creator-mental-health',
        'abc_mental_health_page'
    );
}
add_action('admin_menu', 'abc_admin_menu');

/**
 * Register settings.
 */
function abc_register_settings() {
    register_setting('abc_settings', 'abc_openrouter_api_key');
    register_setting('abc_settings', 'abc_deepseek_model');
    register_setting('abc_settings', 'abc_pexels_api_key');
    register_setting('abc_settings', 'abc_post_frequency');
    register_setting('abc_settings', 'abc_daily_post_count');
    register_setting('abc_settings', 'abc_travel_posts_per_day');
    register_setting('abc_settings', 'abc_mental_health_posts_per_day');
    register_setting('abc_settings', 'abc_human_review');
    register_setting('abc_settings', 'abc_default_travel_keywords');
    register_setting('abc_settings', 'abc_default_mental_health_keywords');
    register_setting('abc_settings', 'abc_use_wikipedia');
}
add_action('admin_init', 'abc_register_settings');

/**
 * Add action for scheduled post creation.
 */
add_action('abc_scheduled_post_creation', 'abc_create_scheduled_post');

/**
 * Create scheduled posts (daily batch of 10 posts: 5 travel + 5 mental health).
 */
function abc_create_scheduled_post() {
    // Check if the post generation functions exist
    if (!function_exists('abc_generate_travel_post') || !function_exists('abc_generate_mental_health_post')) {
        error_log('AI Auto Blog Creator: Post generation functions not found');
        return;
    }

    $travel_count = get_option('abc_travel_posts_per_day', 5);
    $mental_health_count = get_option('abc_mental_health_posts_per_day', 5);

    // Generate travel posts
    for ($i = 0; $i < $travel_count; $i++) {
        abc_generate_travel_post();
        // Add small delay to prevent API rate limiting
        sleep(2);
    }

    // Generate mental health posts
    for ($i = 0; $i < $mental_health_count; $i++) {
        abc_generate_mental_health_post();
        // Add small delay to prevent API rate limiting
        sleep(2);
    }

    // Log the completion
    error_log('AI Auto Blog Creator: Daily batch completed - ' . $travel_count . ' travel posts, ' . $mental_health_count . ' mental health posts');
}

/**
 * Add fitness page functionality.
 */
function abc_init_fitness_page() {
    // Add rewrite rule for fitness page
    add_rewrite_rule('^fitness/?$', 'index.php?abc_fitness_page=1', 'top');

    // Add query var
    add_filter('query_vars', function($vars) {
        $vars[] = 'abc_fitness_page';
        return $vars;
    });

    // Handle template redirect
    add_action('template_redirect', function() {
        if (get_query_var('abc_fitness_page')) {
            abc_display_fitness_page();
            exit;
        }
    });
}
add_action('init', 'abc_init_fitness_page');

/**
 * Display the fitness page.
 */
function abc_display_fitness_page() {
    // Enqueue styles
    wp_enqueue_style('abc-fitness-style', ABC_ASSETS_URL . 'css/fitness-page.css', array(), ABC_VERSION);

    // Get fitness posts
    $args = array(
        'post_type'      => 'post',
        'posts_per_page' => 10,
        'post_status'    => 'publish',
        'meta_query'     => array(
            array(
                'key'     => '_abc_auto_generated',
                'value'   => '1',
                'compare' => '=',
            ),
        ),
        'tax_query' => array(
            array(
                'taxonomy' => 'category',
                'field'    => 'name',
                'terms'    => 'Fitness',
            ),
        ),
    );

    $fitness_posts = new WP_Query($args);

    // Load header
    get_header();

    echo '<div class="abc-fitness-page">';
    echo '<div class="container">';
    echo '<h1>Fitness Blog Posts</h1>';
    echo '<p>Discover our latest fitness guides, workout tips, and health advice.</p>';

    if ($fitness_posts->have_posts()) {
        echo '<div class="abc-fitness-posts">';
        while ($fitness_posts->have_posts()) {
            $fitness_posts->the_post();
            echo '<article class="abc-fitness-post">';
            echo '<h2><a href="' . get_permalink() . '">' . get_the_title() . '</a></h2>';
            echo '<div class="abc-post-meta">';
            echo '<span class="abc-post-date">' . get_the_date() . '</span>';
            echo '</div>';
            if (has_post_thumbnail()) {
                echo '<div class="abc-post-thumbnail">';
                echo '<a href="' . get_permalink() . '">' . get_the_post_thumbnail('medium') . '</a>';
                echo '</div>';
            }
            echo '<div class="abc-post-excerpt">' . get_the_excerpt() . '</div>';
            echo '<a href="' . get_permalink() . '" class="abc-read-more">Read More</a>';
            echo '</article>';
        }
        echo '</div>';

        // Pagination
        echo '<div class="abc-pagination">';
        echo paginate_links(array(
            'total' => $fitness_posts->max_num_pages,
        ));
        echo '</div>';
    } else {
        echo '<p>No fitness posts found. <a href="' . admin_url('admin.php?page=auto-blog-create-generate') . '">Generate some posts</a> to get started!</p>';
    }

    echo '</div>';
    echo '</div>';

    wp_reset_postdata();

    // Load footer
    get_footer();
}

/**
 * Add fitness posts shortcode.
 */
function abc_fitness_posts_shortcode($atts) {
    $atts = shortcode_atts(array(
        'count' => 5,
        'show_excerpt' => 'true',
        'show_image' => 'true',
    ), $atts);

    $args = array(
        'post_type'      => 'post',
        'posts_per_page' => intval($atts['count']),
        'post_status'    => 'publish',
        'meta_query'     => array(
            array(
                'key'     => '_abc_auto_generated',
                'value'   => '1',
                'compare' => '=',
            ),
        ),
        'tax_query' => array(
            array(
                'taxonomy' => 'category',
                'field'    => 'name',
                'terms'    => 'Fitness',
            ),
        ),
    );

    $fitness_posts = new WP_Query($args);

    $output = '<div class="abc-fitness-shortcode">';

    if ($fitness_posts->have_posts()) {
        while ($fitness_posts->have_posts()) {
            $fitness_posts->the_post();
            $output .= '<article class="abc-fitness-item">';
            $output .= '<h3><a href="' . get_permalink() . '">' . get_the_title() . '</a></h3>';

            if ($atts['show_image'] === 'true' && has_post_thumbnail()) {
                $output .= '<div class="abc-item-thumbnail">';
                $output .= '<a href="' . get_permalink() . '">' . get_the_post_thumbnail('thumbnail') . '</a>';
                $output .= '</div>';
            }

            if ($atts['show_excerpt'] === 'true') {
                $output .= '<div class="abc-item-excerpt">' . get_the_excerpt() . '</div>';
            }

            $output .= '<a href="' . get_permalink() . '" class="abc-item-link">Read More</a>';
            $output .= '</article>';
        }
    } else {
        $output .= '<p>No fitness posts found.</p>';
    }

    $output .= '</div>';

    wp_reset_postdata();

    return $output;
}
add_shortcode('fitness_posts', 'abc_fitness_posts_shortcode');

/**
 * Debug function to check cron status and troubleshoot automatic posting.
 */
function abc_debug_cron_status() {
    if (!current_user_can('manage_options')) {
        return;
    }

    $debug_info = array();

    // Check if cron is scheduled
    $next_scheduled = wp_next_scheduled('abc_scheduled_post_creation');
    $debug_info['cron_scheduled'] = $next_scheduled;
    $debug_info['cron_scheduled_human'] = $next_scheduled ? date('Y-m-d H:i:s', $next_scheduled) : 'Not scheduled';

    // Check if WordPress cron is disabled
    $debug_info['wp_cron_disabled'] = defined('DISABLE_WP_CRON') && DISABLE_WP_CRON;

    // Check if required functions exist
    $debug_info['travel_function_exists'] = function_exists('abc_generate_travel_post');
    $debug_info['mental_health_function_exists'] = function_exists('abc_generate_mental_health_post');

    // Check API configuration
    $debug_info['openrouter_api_configured'] = !empty(get_option('abc_openrouter_api_key'));
    $debug_info['pexels_api_configured'] = !empty(get_option('abc_pexels_api_key'));

    // Check post settings
    $debug_info['travel_posts_per_day'] = get_option('abc_travel_posts_per_day', 5);
    $debug_info['mental_health_posts_per_day'] = get_option('abc_mental_health_posts_per_day', 5);
    $debug_info['post_frequency'] = get_option('abc_post_frequency', 'daily');
    $debug_info['human_review'] = get_option('abc_human_review', 1);

    // Check recent posts
    $recent_posts = get_posts(array(
        'meta_key' => '_abc_auto_generated',
        'meta_value' => '1',
        'posts_per_page' => 5,
        'post_status' => array('publish', 'draft'),
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    $debug_info['recent_auto_posts_count'] = count($recent_posts);
    $debug_info['last_auto_post_date'] = !empty($recent_posts) ? $recent_posts[0]->post_date : 'None found';

    return $debug_info;
}

/**
 * AJAX handler for cron debug information.
 */
function abc_ajax_debug_cron() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
    }

    $debug_info = abc_debug_cron_status();
    wp_send_json_success($debug_info);
}
add_action('wp_ajax_abc_debug_cron', 'abc_ajax_debug_cron');

/**
 * Manual trigger for scheduled post creation (for testing).
 */
function abc_ajax_manual_trigger() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
    }

    // Log the manual trigger
    error_log('AI Auto Blog Creator: Manual trigger initiated by user');

    // Call the scheduled function directly
    abc_create_scheduled_post();

    wp_send_json_success('Manual trigger completed. Check error logs for details.');
}
add_action('wp_ajax_abc_manual_trigger', 'abc_ajax_manual_trigger');

/**
 * Force reschedule the cron job.
 */
function abc_ajax_reschedule_cron() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
    }

    // Clear existing schedule
    wp_clear_scheduled_hook('abc_scheduled_post_creation');

    // Reschedule
    $frequency = get_option('abc_post_frequency', 'daily');
    $result = wp_schedule_event(time(), $frequency, 'abc_scheduled_post_creation');

    if ($result !== false) {
        error_log('AI Auto Blog Creator: Cron job rescheduled successfully');
        wp_send_json_success('Cron job rescheduled successfully');
    } else {
        error_log('AI Auto Blog Creator: Failed to reschedule cron job');
        wp_send_json_error('Failed to reschedule cron job');
    }
}
add_action('wp_ajax_abc_reschedule_cron', 'abc_ajax_reschedule_cron');
