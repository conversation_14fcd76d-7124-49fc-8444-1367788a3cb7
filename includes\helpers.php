<?php
/**
 * Helper functions for Auto Blog Create plugin.
 *
 * @package Auto_Blog_Create
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Check if a string contains plagiarized content.
 *
 * This is a simple implementation that could be expanded with a real
 * plagiarism checking API in a production environment.
 *
 * @param string $content The content to check.
 * @return bool True if plagiarism is detected, false otherwise.
 */
function abc_check_plagiarism($content) {
    // This is a placeholder function
    // In a real implementation, you would integrate with a plagiarism checking API

    // For now, we'll just return false (no plagiarism detected)
    return false;
}

/**
 * Get a comprehensive list of travel destinations.
 *
 * @return array List of travel destinations.
 */
function abc_get_travel_destinations() {
    return array(
        // Major Cities
        'Paris', 'Tokyo', 'New York', 'London', 'Rome', 'Sydney', 'Barcelona', 'Amsterdam', 'Dubai', 'Singapore',
        'Bangkok', 'Istanbul', 'Prague', 'San Francisco', 'Kyoto', 'Berlin', 'Vienna', 'Madrid', 'Lisbon', 'Stockholm',

        // Tropical Destinations
        'Bali', 'Maldives', 'Hawaii', 'Fiji', 'Seychelles', 'Mauritius', 'Phuket', 'Santorini', 'Mykonos', 'Ibiza',

        // Adventure Destinations
        'Nepal', 'Patagonia', 'Iceland', 'New Zealand', 'Costa Rica', 'Peru', 'Norway', 'Switzerland', 'Canada', 'Alaska',

        // Cultural Destinations
        'India', 'Morocco', 'Egypt', 'Jordan', 'China', 'Vietnam', 'Cambodia', 'Myanmar', 'Ethiopia', 'Iran',

        // Beach Destinations
        'Maldives', 'Bahamas', 'Barbados', 'Aruba', 'Turks and Caicos', 'Antigua', 'St. Lucia', 'Bermuda', 'Cyprus', 'Malta',

        // Historical Destinations
        'Greece', 'Italy', 'Turkey', 'Israel', 'Palestine', 'Lebanon', 'Syria', 'Iraq', 'Iran', 'Afghanistan',

        // Wildlife Destinations
        'Kenya', 'Tanzania', 'South Africa', 'Botswana', 'Namibia', 'Madagascar', 'Galapagos', 'Borneo', 'Sri Lanka', 'Rwanda',

        // Unique Destinations
        'Antarctica', 'Greenland', 'Faroe Islands', 'Socotra Island', 'Easter Island', 'Bhutan', 'North Korea', 'Mongolia', 'Uzbekistan', 'Kazakhstan'
    );
}

/**
 * Get a comprehensive list of mental health topics.
 *
 * @return array List of mental health topics.
 */
function abc_get_mental_health_topics() {
    return array(
        // Common Mental Health Conditions
        'Anxiety', 'Depression', 'Stress Management', 'Panic Attacks', 'Social Anxiety', 'Generalized Anxiety Disorder',
        'PTSD', 'OCD', 'Bipolar Disorder', 'ADHD', 'Eating Disorders', 'Insomnia', 'Seasonal Affective Disorder',

        // Self-Care and Wellness
        'Mindfulness', 'Meditation', 'Self-Care', 'Work-Life Balance', 'Burnout Prevention', 'Emotional Intelligence',
        'Resilience Building', 'Positive Psychology', 'Gratitude Practice', 'Breathing Exercises', 'Progressive Muscle Relaxation',

        // Relationships and Social Health
        'Healthy Relationships', 'Setting Boundaries', 'Communication Skills', 'Conflict Resolution', 'Loneliness',
        'Social Support', 'Family Dynamics', 'Friendship', 'Dating and Mental Health', 'Divorce and Separation',

        // Life Transitions and Challenges
        'Grief and Loss', 'Career Stress', 'Financial Anxiety', 'Parenting Stress', 'Empty Nest Syndrome',
        'Retirement Adjustment', 'Moving and Relocation', 'Chronic Illness', 'Disability and Mental Health', 'Aging and Mental Health',

        // Therapy and Treatment
        'Cognitive Behavioral Therapy', 'Therapy Benefits', 'Finding a Therapist', 'Medication Management',
        'Support Groups', 'Crisis Intervention', 'Mental Health First Aid', 'Suicide Prevention', 'Recovery Process',

        // Lifestyle and Mental Health
        'Exercise and Mental Health', 'Nutrition and Mood', 'Sleep Hygiene', 'Digital Detox', 'Nature Therapy',
        'Art Therapy', 'Music Therapy', 'Pet Therapy', 'Journaling', 'Hobby Benefits'
    );
}

/**
 * Get travel-related keywords.
 *
 * @return array List of travel keywords.
 */
function abc_get_travel_keywords() {
    return array(
        'adventure travel',
        'budget travel',
        'luxury travel',
        'family-friendly',
        'solo travel',
        'backpacking',
        'cultural experience',
        'food tourism',
        'eco-tourism',
        'beach vacation',
        'city break',
        'road trip',
        'honeymoon',
        'historical sites',
        'natural wonders',
        'off the beaten path',
        'weekend getaway',
        'all-inclusive resort',
        'cruise',
        'hiking',
    );
}

/**
 * Generate random travel keywords.
 *
 * @param int $count Number of keywords to generate.
 * @return string Comma-separated keywords.
 */
function abc_generate_random_keywords($count = 3) {
    $all_keywords = abc_get_travel_keywords();
    $selected_keywords = array_rand(array_flip($all_keywords), min($count, count($all_keywords)));

    return implode(', ', $selected_keywords);
}

/**
 * Get mental health-related keywords.
 *
 * @return array List of mental health keywords.
 */
function abc_get_mental_health_keywords() {
    return array(
        'mental health', 'wellness', 'self-care', 'therapy', 'counseling', 'mindfulness', 'meditation',
        'stress relief', 'anxiety management', 'depression support', 'emotional wellbeing', 'mental wellness',
        'psychological health', 'coping strategies', 'resilience', 'self-help', 'mental fitness',
        'emotional intelligence', 'positive psychology', 'mental health awareness', 'psychological support',
        'therapeutic techniques', 'mental health resources', 'wellness tips', 'emotional support',
        'mental health education', 'psychological wellness', 'mental health advocacy', 'wellness journey'
    );
}

/**
 * Generate random mental health keywords.
 *
 * @param int $count Number of keywords to generate.
 * @return string Comma-separated keywords.
 */
function abc_generate_random_mental_health_keywords($count = 3) {
    $all_keywords = abc_get_mental_health_keywords();
    $selected_keywords = array_rand(array_flip($all_keywords), min($count, count($all_keywords)));

    return implode(', ', $selected_keywords);
}

/**
 * Check if the plugin settings are configured.
 *
 * @return bool True if settings are configured, false otherwise.
 */
function abc_is_configured() {
    $openrouter_api_key = get_option('abc_openrouter_api_key');
    $pexels_api_key = get_option('abc_pexels_api_key');

    return !empty($openrouter_api_key) && !empty($pexels_api_key);
}

/**
 * Add custom cron schedules.
 *
 * @param array $schedules Existing cron schedules.
 * @return array Modified cron schedules.
 */
function abc_add_cron_schedules($schedules) {
    // Add monthly schedule if it doesn't exist
    if (!isset($schedules['monthly'])) {
        $schedules['monthly'] = array(
            'interval' => 30 * DAY_IN_SECONDS,
            'display'  => __('Once Monthly', 'auto-blog-create'),
        );
    }

    return $schedules;
}
add_filter('cron_schedules', 'abc_add_cron_schedules');

/**
 * Format a destination name for use in URLs and queries.
 *
 * @param string $destination The destination name.
 * @return string The formatted destination.
 */
function abc_format_destination($destination) {
    // Convert to lowercase
    $formatted = strtolower($destination);

    // Replace spaces with hyphens
    $formatted = str_replace(' ', '-', $formatted);

    // Remove special characters
    $formatted = preg_replace('/[^a-z0-9\-]/', '', $formatted);

    return $formatted;
}

/**
 * Get the plugin admin URL.
 *
 * @param string $page The admin page slug.
 * @return string The admin URL.
 */
function abc_get_admin_url($page = '') {
    $base_url = admin_url('admin.php?page=auto-blog-create');

    if ($page) {
        return admin_url('admin.php?page=auto-blog-create-' . $page);
    }

    return $base_url;
}

/**
 * Display admin notices.
 */
function abc_admin_notices() {
    // Check if we're on a plugin page
    $screen = get_current_screen();
    if (!$screen || strpos($screen->id, 'auto-blog-create') === false) {
        return;
    }

    // Check if settings are configured
    if (!abc_is_configured()) {
        ?>
        <div class="notice notice-warning is-dismissible">
            <p>
                <?php
                printf(
                    __('Auto Blog Create plugin is not fully configured. Please <a href="%s">configure the settings</a> to enable all features.', 'auto-blog-create'),
                    abc_get_admin_url('settings')
                );
                ?>
            </p>
        </div>
        <?php
    }
}
add_action('admin_notices', 'abc_admin_notices');

/**
 * Check if a post is auto-generated.
 *
 * @param int $post_id The post ID.
 * @return bool True if the post is auto-generated, false otherwise.
 */
function abc_is_auto_generated_post($post_id) {
    return get_post_meta($post_id, '_abc_auto_generated', true) === '1';
}

/**
 * Add a filter to display a notice on auto-generated posts.
 *
 * @param string $content The post content.
 * @return string The filtered content.
 */
function abc_add_auto_generated_notice($content) {
    // Only add notice on single post pages
    if (!is_single()) {
        return $content;
    }

    // Check if this is an auto-generated post
    if (abc_is_auto_generated_post(get_the_ID())) {
        $notice = '<div class="abc-auto-generated-notice">';
        $notice .= '<p><em>' . __('This post was automatically generated by AI. Always verify details locally before making travel plans.', 'auto-blog-create') . '</em></p>';
        $notice .= '</div>';

        // Add notice at the beginning of the content
        $content = $notice . $content;
    }

    return $content;
}
add_filter('the_content', 'abc_add_auto_generated_notice');
