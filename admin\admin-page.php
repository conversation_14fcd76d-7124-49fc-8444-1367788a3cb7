<?php
/**
 * Admin page functions for Auto Blog Create plugin.
 *
 * @package Auto_Blog_Create
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Display the main admin page.
 */
function abc_admin_page() {
    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
        <div class="abc-admin-container">
            <div class="abc-admin-main">
                <div class="abc-card">
                    <h2><?php _e('Welcome to AI Auto Blog Creator', 'ai-auto-blog-creator'); ?></h2>
                    <p><?php _e('This plugin automatically generates 10 daily SEO-optimized blog posts (5 travel, 5 mental health) using Deepseek R1 API from OpenRouter with Wikipedia integration.', 'ai-auto-blog-creator'); ?></p>
                    <p><?php _e('Use the navigation below to configure the plugin or generate new posts.', 'ai-auto-blog-creator'); ?></p>

                    <div class="abc-quick-links">
                        <a href="<?php echo admin_url('admin.php?page=ai-auto-blog-creator-generate'); ?>" class="button button-primary">
                            <?php _e('Generate New Posts', 'ai-auto-blog-creator'); ?>
                        </a>
                        <a href="<?php echo admin_url('admin.php?page=ai-auto-blog-creator-settings'); ?>" class="button">
                            <?php _e('Settings', 'ai-auto-blog-creator'); ?>
                        </a>
                    </div>
                </div>

                <div class="abc-card">
                    <h2><?php _e('Recent Auto-Generated Posts', 'auto-blog-create'); ?></h2>
                    <?php
                    $args = array(
                        'post_type'      => 'post',
                        'posts_per_page' => 5,
                        'meta_query'     => array(
                            array(
                                'key'     => '_abc_auto_generated',
                                'value'   => '1',
                                'compare' => '=',
                            ),
                        ),
                    );

                    $query = new WP_Query($args);

                    if ($query->have_posts()) {
                        echo '<ul class="abc-recent-posts">';
                        while ($query->have_posts()) {
                            $query->the_post();
                            echo '<li>';
                            echo '<a href="' . get_edit_post_link() . '">' . get_the_title() . '</a>';
                            echo ' - ' . get_the_date();
                            echo ' - <span class="abc-post-status">' . get_post_status() . '</span>';
                            echo '</li>';
                        }
                        echo '</ul>';
                        wp_reset_postdata();
                    } else {
                        echo '<p>' . __('No auto-generated posts found.', 'auto-blog-create') . '</p>';
                    }
                    ?>
                </div>
            </div>

            <div class="abc-admin-sidebar">
                <div class="abc-card">
                    <h3><?php _e('Plugin Status', 'auto-blog-create'); ?></h3>
                    <ul class="abc-status-list">
                        <li>
                            <span class="abc-status-label"><?php _e('Content Source:', 'ai-auto-blog-creator'); ?></span>
                            <span class="abc-status-value <?php echo get_option('abc_use_wikipedia') ? 'abc-status-warning' : 'abc-status-ok'; ?>">
                                <?php echo get_option('abc_use_wikipedia') ? __('Wikipedia (Fallback)', 'ai-auto-blog-creator') : __('OpenRouter Deepseek R1', 'ai-auto-blog-creator'); ?>
                            </span>
                        </li>
                        <li>
                            <span class="abc-status-label"><?php _e('OpenRouter API:', 'ai-auto-blog-creator'); ?></span>
                            <span class="abc-status-value <?php echo get_option('abc_openrouter_api_key') ? 'abc-status-ok' : 'abc-status-error'; ?>">
                                <?php echo get_option('abc_openrouter_api_key') ? __('Configured', 'ai-auto-blog-creator') : __('Not Configured', 'ai-auto-blog-creator'); ?>
                            </span>
                        </li>
                        <li>
                            <span class="abc-status-label"><?php _e('Pexels API:', 'auto-blog-create'); ?></span>
                            <span class="abc-status-value <?php echo get_option('abc_pexels_api_key') ? 'abc-status-ok' : 'abc-status-error'; ?>">
                                <?php echo get_option('abc_pexels_api_key') ? __('Configured', 'auto-blog-create') : __('Not Configured', 'auto-blog-create'); ?>
                            </span>
                        </li>
                        <li>
                            <span class="abc-status-label"><?php _e('Auto-Posting:', 'auto-blog-create'); ?></span>
                            <span class="abc-status-value <?php echo wp_next_scheduled('abc_scheduled_post_creation') ? 'abc-status-ok' : 'abc-status-error'; ?>">
                                <?php
                                $next_scheduled = wp_next_scheduled('abc_scheduled_post_creation');
                                if ($next_scheduled) {
                                    echo __('Scheduled', 'auto-blog-create') . ' (' . date('Y-m-d H:i:s', $next_scheduled) . ')';
                                } else {
                                    echo __('Not Scheduled', 'auto-blog-create');
                                }
                                ?>
                            </span>
                        </li>
                        <li>
                            <span class="abc-status-label"><?php _e('WordPress Cron:', 'auto-blog-create'); ?></span>
                            <span class="abc-status-value <?php echo (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) ? 'abc-status-error' : 'abc-status-ok'; ?>">
                                <?php echo (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) ? __('Disabled', 'auto-blog-create') : __('Enabled', 'auto-blog-create'); ?>
                            </span>
                        </li>
                        <li>
                            <span class="abc-status-label"><?php _e('Recent Auto Posts:', 'auto-blog-create'); ?></span>
                            <span class="abc-status-value abc-status-info">
                                <?php
                                $recent_posts = get_posts(array(
                                    'meta_key' => '_abc_auto_generated',
                                    'meta_value' => '1',
                                    'posts_per_page' => 1,
                                    'post_status' => array('publish', 'draft'),
                                    'orderby' => 'date',
                                    'order' => 'DESC'
                                ));
                                if (!empty($recent_posts)) {
                                    echo count($recent_posts) . ' found (last: ' . $recent_posts[0]->post_date . ')';
                                } else {
                                    echo __('None found', 'auto-blog-create');
                                }
                                ?>
                            </span>
                        </li>
                    </ul>
                </div>

                <div class="abc-card">
                    <h3><?php _e('Debug & Testing', 'auto-blog-create'); ?></h3>
                    <div class="abc-debug-actions">
                        <button type="button" class="button" onclick="debugCronStatus()"><?php _e('Check Cron Status', 'auto-blog-create'); ?></button>
                        <button type="button" class="button" onclick="manualTrigger()"><?php _e('Manual Trigger (Test)', 'auto-blog-create'); ?></button>
                        <button type="button" class="button" onclick="rescheduleCron()"><?php _e('Reschedule Cron', 'auto-blog-create'); ?></button>
                        <a href="<?php echo plugin_dir_url(dirname(__FILE__)) . 'cron-debug.php'; ?>" class="button" target="_blank"><?php _e('Full Debug Tool', 'auto-blog-create'); ?></a>
                    </div>
                    <div id="debug-results" style="margin-top: 15px;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function debugCronStatus() {
        var resultDiv = document.getElementById('debug-results');
        resultDiv.innerHTML = '<p>Checking cron status...</p>';

        jQuery.post(ajaxurl, {
            action: 'abc_debug_cron',
            nonce: '<?php echo wp_create_nonce('abc_ajax_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                var data = response.data;
                var html = '<div style="background: #f0f0f1; padding: 10px; border-radius: 3px;">';
                html += '<h4>Cron Debug Information:</h4>';
                html += '<ul>';
                html += '<li><strong>Cron Scheduled:</strong> ' + (data.cron_scheduled ? 'Yes (' + data.cron_scheduled_human + ')' : 'No') + '</li>';
                html += '<li><strong>WP Cron Disabled:</strong> ' + (data.wp_cron_disabled ? 'Yes' : 'No') + '</li>';
                html += '<li><strong>Travel Function Exists:</strong> ' + (data.travel_function_exists ? 'Yes' : 'No') + '</li>';
                html += '<li><strong>Mental Health Function Exists:</strong> ' + (data.mental_health_function_exists ? 'Yes' : 'No') + '</li>';
                html += '<li><strong>OpenRouter API Configured:</strong> ' + (data.openrouter_api_configured ? 'Yes' : 'No') + '</li>';
                html += '<li><strong>Pexels API Configured:</strong> ' + (data.pexels_api_configured ? 'Yes' : 'No') + '</li>';
                html += '<li><strong>Travel Posts Per Day:</strong> ' + data.travel_posts_per_day + '</li>';
                html += '<li><strong>Mental Health Posts Per Day:</strong> ' + data.mental_health_posts_per_day + '</li>';
                html += '<li><strong>Post Frequency:</strong> ' + data.post_frequency + '</li>';
                html += '<li><strong>Human Review:</strong> ' + (data.human_review ? 'Enabled (drafts)' : 'Disabled (auto-publish)') + '</li>';
                html += '<li><strong>Recent Auto Posts:</strong> ' + data.recent_auto_posts_count + '</li>';
                html += '<li><strong>Last Auto Post:</strong> ' + data.last_auto_post_date + '</li>';
                html += '</ul></div>';
                resultDiv.innerHTML = html;
            } else {
                resultDiv.innerHTML = '<p style="color: red;">Error: ' + response.data + '</p>';
            }
        }).fail(function() {
            resultDiv.innerHTML = '<p style="color: red;">AJAX request failed</p>';
        });
    }

    function manualTrigger() {
        var resultDiv = document.getElementById('debug-results');
        resultDiv.innerHTML = '<p>Triggering manual post creation...</p>';

        jQuery.post(ajaxurl, {
            action: 'abc_manual_trigger',
            nonce: '<?php echo wp_create_nonce('abc_ajax_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                resultDiv.innerHTML = '<p style="color: green;">✓ ' + response.data + '</p>';
            } else {
                resultDiv.innerHTML = '<p style="color: red;">✗ ' + response.data + '</p>';
            }
        }).fail(function() {
            resultDiv.innerHTML = '<p style="color: red;">AJAX request failed</p>';
        });
    }

    function rescheduleCron() {
        var resultDiv = document.getElementById('debug-results');
        resultDiv.innerHTML = '<p>Rescheduling cron job...</p>';

        jQuery.post(ajaxurl, {
            action: 'abc_reschedule_cron',
            nonce: '<?php echo wp_create_nonce('abc_ajax_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                resultDiv.innerHTML = '<p style="color: green;">✓ ' + response.data + '</p>';
                // Refresh the page after 2 seconds to update status
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else {
                resultDiv.innerHTML = '<p style="color: red;">✗ ' + response.data + '</p>';
            }
        }).fail(function() {
            resultDiv.innerHTML = '<p style="color: red;">AJAX request failed</p>';
        });
    }
    </script>
    <?php
}

/**
 * Display the settings page.
 */
function abc_settings_page() {
    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

        <?php
        // Show migration notice if needed
        if (get_option('abc_migration_notice') && get_option('abc_unsplash_access_key') && !get_option('abc_pexels_api_key')) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>' . __('Migration Notice:', 'auto-blog-create') . '</strong> ';
            echo __('We have switched from Unsplash to Pexels API for better image quality and reliability. Please get your free Pexels API key and update your settings.', 'auto-blog-create');
            echo ' <a href="https://www.pexels.com/api/" target="_blank">' . __('Get Pexels API Key', 'auto-blog-create') . '</a></p>';
            echo '</div>';
        }
        ?>

        <form method="post" action="options.php">
            <?php
            settings_fields('abc_settings');
            do_settings_sections('abc_settings');
            ?>

            <table class="form-table">
                <tr valign="top">
                    <th scope="row"><?php _e('OpenRouter API Key', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <input type="password" name="abc_openrouter_api_key" value="<?php echo esc_attr(get_option('abc_openrouter_api_key')); ?>" class="regular-text" />
                        <p class="description">
                            <?php _e('Enter your OpenRouter API key to access Deepseek R1 model.', 'ai-auto-blog-creator'); ?>
                            <a href="https://openrouter.ai/" target="_blank"><?php _e('Get your API key here', 'ai-auto-blog-creator'); ?></a>
                        </p>
                        <button type="button" class="button" onclick="testAPI('openrouter')"><?php _e('Test OpenRouter API', 'ai-auto-blog-creator'); ?></button>
                        <div id="openrouter-test-result"></div>
                    </td>
                </tr>

                <tr valign="top">
                    <th scope="row"><?php _e('Deepseek Model', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <select name="abc_deepseek_model">
                            <option value="deepseek/deepseek-r1" <?php selected(get_option('abc_deepseek_model'), 'deepseek/deepseek-r1'); ?>>
                                deepseek/deepseek-r1
                            </option>
                            <option value="deepseek/deepseek-chat" <?php selected(get_option('abc_deepseek_model'), 'deepseek/deepseek-chat'); ?>>
                                deepseek/deepseek-chat
                            </option>
                        </select>
                        <p class="description"><?php _e('Select the Deepseek model to use for content generation.', 'ai-auto-blog-creator'); ?></p>
                    </td>
                </tr>

                <tr valign="top">
                    <th scope="row"><?php _e('Pexels API Key', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <input type="text" name="abc_pexels_api_key" value="<?php echo esc_attr(get_option('abc_pexels_api_key')); ?>" class="regular-text" />
                        <p class="description">
                            <?php _e('Enter your Pexels API key for featured images.', 'ai-auto-blog-creator'); ?>
                            <a href="https://www.pexels.com/api/" target="_blank"><?php _e('Get your free API key here', 'ai-auto-blog-creator'); ?></a>
                        </p>
                        <button type="button" class="button" onclick="testAPI('pexels')"><?php _e('Test Pexels API', 'ai-auto-blog-creator'); ?></button>
                        <div id="pexels-test-result"></div>
                    </td>
                </tr>

                <tr valign="top">
                    <th scope="row"><?php _e('Post Frequency', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <select name="abc_post_frequency">
                            <option value="daily" <?php selected(get_option('abc_post_frequency'), 'daily'); ?>>
                                <?php _e('Daily (Recommended)', 'ai-auto-blog-creator'); ?>
                            </option>
                            <option value="weekly" <?php selected(get_option('abc_post_frequency'), 'weekly'); ?>>
                                <?php _e('Weekly', 'ai-auto-blog-creator'); ?>
                            </option>
                            <option value="monthly" <?php selected(get_option('abc_post_frequency'), 'monthly'); ?>>
                                <?php _e('Monthly', 'ai-auto-blog-creator'); ?>
                            </option>
                        </select>
                        <p class="description"><?php _e('Select how often to automatically generate posts. Daily generates 10 posts (5 travel + 5 mental health).', 'ai-auto-blog-creator'); ?></p>
                    </td>
                </tr>

                <tr valign="top">
                    <th scope="row"><?php _e('Travel Posts Per Day', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <input type="number" name="abc_travel_posts_per_day" value="<?php echo esc_attr(get_option('abc_travel_posts_per_day', 5)); ?>" min="1" max="20" class="small-text" />
                        <p class="description"><?php _e('Number of travel posts to generate daily.', 'ai-auto-blog-creator'); ?></p>
                    </td>
                </tr>

                <tr valign="top">
                    <th scope="row"><?php _e('Mental Health Posts Per Day', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <input type="number" name="abc_mental_health_posts_per_day" value="<?php echo esc_attr(get_option('abc_mental_health_posts_per_day', 5)); ?>" min="1" max="20" class="small-text" />
                        <p class="description"><?php _e('Number of mental health posts to generate daily.', 'ai-auto-blog-creator'); ?></p>
                    </td>
                </tr>

                <tr valign="top">
                    <th scope="row"><?php _e('Human Review', 'auto-blog-create'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="abc_human_review" value="1" <?php checked(get_option('abc_human_review'), 1); ?> />
                            <?php _e('Require human review before publishing', 'auto-blog-create'); ?>
                        </label>
                        <p class="description"><?php _e('If checked, posts will be saved as drafts for review before publishing.', 'auto-blog-create'); ?></p>
                    </td>
                </tr>

                <tr valign="top">
                    <th scope="row"><?php _e('Default Travel Keywords', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <input type="text" name="abc_default_travel_keywords" value="<?php echo esc_attr(get_option('abc_default_travel_keywords', 'travel, destination, vacation, tourism')); ?>" class="regular-text" />
                        <p class="description"><?php _e('Enter default keywords for travel posts (comma-separated).', 'ai-auto-blog-creator'); ?></p>
                    </td>
                </tr>

                <tr valign="top">
                    <th scope="row"><?php _e('Default Mental Health Keywords', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <input type="text" name="abc_default_mental_health_keywords" value="<?php echo esc_attr(get_option('abc_default_mental_health_keywords', 'mental health, wellness, self-care, therapy')); ?>" class="regular-text" />
                        <p class="description"><?php _e('Enter default keywords for mental health posts (comma-separated).', 'ai-auto-blog-creator'); ?></p>
                    </td>
                </tr>

                <tr valign="top">
                    <th scope="row"><?php _e('Use Wikipedia Content', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="abc_use_wikipedia" value="1" <?php checked(get_option('abc_use_wikipedia'), 1); ?> />
                            <?php _e('Use Wikipedia as primary content source instead of OpenRouter API', 'ai-auto-blog-creator'); ?>
                        </label>
                        <p class="description"><?php _e('If checked, the plugin will use Wikipedia as the content source instead of OpenRouter Deepseek R1 API. Useful for testing or when API quota is reached.', 'ai-auto-blog-creator'); ?></p>
                    </td>
                </tr>
            </table>

            <?php submit_button(); ?>
        </form>

        <script>
        function testAPI(apiType) {
            var resultDiv = document.getElementById(apiType + '-test-result');
            resultDiv.innerHTML = '<p>Testing connection...</p>';

            var data = {
                'action': 'abc_test_api_connection',
                'api': apiType,
                'nonce': '<?php echo wp_create_nonce('abc_ajax_nonce'); ?>'
            };

            jQuery.post(ajaxurl, data, function(response) {
                if (response.success) {
                    resultDiv.innerHTML = '<p style="color: green;">✓ ' + response.data.message + '</p>';
                } else {
                    resultDiv.innerHTML = '<p style="color: red;">✗ ' + response.data.message + '</p>';
                }
            }).fail(function() {
                resultDiv.innerHTML = '<p style="color: red;">✗ Connection failed</p>';
            });
        }
        </script>
    </div>
    <?php
}

/**
 * Display the generate post page.
 */
function abc_generate_page() {
    // Check if form was submitted
    if (isset($_POST['abc_generate_post']) && check_admin_referer('abc_generate_post_nonce')) {
        $content_type = sanitize_text_field($_POST['abc_content_type']);
        $topic = sanitize_text_field($_POST['abc_topic']);

        $post_id = false;

        // Generate the post based on content type
        if ($content_type === 'travel' && function_exists('abc_generate_travel_post')) {
            $post_id = abc_generate_travel_post($topic);
        } elseif ($content_type === 'mental_health' && function_exists('abc_generate_mental_health_post')) {
            $post_id = abc_generate_mental_health_post($topic);
        } elseif ($content_type === 'fitness' && function_exists('abc_generate_fitness_post')) {
            $keywords = sanitize_text_field($_POST['abc_keywords']);
            $post_id = abc_generate_fitness_post($topic, $keywords);
        } else {
            echo '<div class="notice notice-error is-dismissible"><p>';
            _e('Error: Post generation function not found. Please check that all plugin files are properly uploaded.', 'ai-auto-blog-creator');
            echo '</p></div>';
        }

        if ($post_id) {
            echo '<div class="notice notice-success is-dismissible"><p>';
            printf(
                __('Post generated successfully! <a href="%s">Edit Post</a>', 'ai-auto-blog-creator'),
                get_edit_post_link($post_id)
            );
            echo '</p></div>';
        } else {
            echo '<div class="notice notice-error is-dismissible"><p>';
            _e('Error generating post. Please check your settings and try again.', 'ai-auto-blog-creator');
            echo '</p></div>';
        }
    }

    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
        <form method="post" action="">
            <?php wp_nonce_field('abc_generate_post_nonce'); ?>

            <table class="form-table">
                <tr valign="top">
                    <th scope="row"><?php _e('Content Type', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <select name="abc_content_type" id="abc_content_type" onchange="updateTopicField()">
                            <option value="travel"><?php _e('Travel', 'ai-auto-blog-creator'); ?></option>
                            <option value="mental_health"><?php _e('Mental Health', 'ai-auto-blog-creator'); ?></option>
                            <option value="fitness"><?php _e('Fitness (Legacy)', 'ai-auto-blog-creator'); ?></option>
                        </select>
                        <p class="description"><?php _e('Select the type of content to generate.', 'ai-auto-blog-creator'); ?></p>
                    </td>
                </tr>

                <tr valign="top">
                    <th scope="row"><?php _e('Topic', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <input type="text" name="abc_topic" value="" class="regular-text" required />
                        <p class="description" id="topic_description"><?php _e('Enter the travel destination (e.g., "Paris", "Tokyo", "Bali").', 'ai-auto-blog-creator'); ?></p>
                    </td>
                </tr>

                <tr valign="top" id="keywords_row" style="display: none;">
                    <th scope="row"><?php _e('Keywords', 'ai-auto-blog-creator'); ?></th>
                    <td>
                        <input type="text" name="abc_keywords" value="<?php echo esc_attr(get_option('abc_default_keywords', 'fitness, workout, health')); ?>" class="regular-text" />
                        <p class="description"><?php _e('Enter keywords for the post (comma-separated). Only used for fitness posts.', 'ai-auto-blog-creator'); ?></p>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" name="abc_generate_post" class="button button-primary" value="<?php _e('Generate Post', 'ai-auto-blog-creator'); ?>" />
            </p>
        </form>

        <script>
        function updateTopicField() {
            var contentType = document.getElementById('abc_content_type').value;
            var topicDescription = document.getElementById('topic_description');
            var keywordsRow = document.getElementById('keywords_row');

            if (contentType === 'travel') {
                topicDescription.textContent = '<?php _e('Enter the travel destination (e.g., "Paris", "Tokyo", "Bali").', 'ai-auto-blog-creator'); ?>';
                keywordsRow.style.display = 'none';
            } else if (contentType === 'mental_health') {
                topicDescription.textContent = '<?php _e('Enter the mental health topic (e.g., "Anxiety", "Depression", "Stress Management").', 'ai-auto-blog-creator'); ?>';
                keywordsRow.style.display = 'none';
            } else if (contentType === 'fitness') {
                topicDescription.textContent = '<?php _e('Enter the fitness topic (e.g., "Weight Loss", "Muscle Building", "Yoga").', 'ai-auto-blog-creator'); ?>';
                keywordsRow.style.display = 'table-row';
            }
        }
        </script>
    </div>
    <?php
}

/**
 * Display the fitness posts page.
 */
function abc_fitness_page() {
    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

        <div class="abc-admin-container">
            <div class="abc-admin-main">
                <div class="abc-card">
                    <h2><?php _e('Fitness Posts Overview', 'auto-blog-create'); ?></h2>
                    <p><?php _e('View and manage all auto-generated fitness blog posts.', 'auto-blog-create'); ?></p>

                    <div class="abc-quick-links">
                        <a href="<?php echo admin_url('admin.php?page=auto-blog-create-generate'); ?>" class="button button-primary">
                            <?php _e('Generate New Fitness Post', 'auto-blog-create'); ?>
                        </a>
                        <a href="<?php echo home_url('/fitness/'); ?>" class="button" target="_blank">
                            <?php _e('View Fitness Page', 'auto-blog-create'); ?>
                        </a>
                    </div>
                </div>

                <div class="abc-card">
                    <h2><?php _e('All Fitness Posts', 'auto-blog-create'); ?></h2>
                    <?php
                    $args = array(
                        'post_type'      => 'post',
                        'posts_per_page' => 20,
                        'meta_query'     => array(
                            array(
                                'key'     => '_abc_auto_generated',
                                'value'   => '1',
                                'compare' => '=',
                            ),
                        ),
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'category',
                                'field'    => 'name',
                                'terms'    => 'Fitness',
                            ),
                        ),
                    );

                    $query = new WP_Query($args);

                    if ($query->have_posts()) {
                        echo '<table class="wp-list-table widefat fixed striped">';
                        echo '<thead><tr><th>Title</th><th>Topic</th><th>Date</th><th>Status</th><th>Actions</th></tr></thead>';
                        echo '<tbody>';
                        while ($query->have_posts()) {
                            $query->the_post();
                            $fitness_topic = get_post_meta(get_the_ID(), '_abc_fitness_topic', true);
                            echo '<tr>';
                            echo '<td><strong>' . get_the_title() . '</strong></td>';
                            echo '<td>' . esc_html($fitness_topic) . '</td>';
                            echo '<td>' . get_the_date() . '</td>';
                            echo '<td><span class="abc-post-status">' . get_post_status() . '</span></td>';
                            echo '<td>';
                            echo '<a href="' . get_edit_post_link() . '" class="button button-small">Edit</a> ';
                            echo '<a href="' . get_permalink() . '" class="button button-small" target="_blank">View</a>';
                            echo '</td>';
                            echo '</tr>';
                        }
                        echo '</tbody>';
                        echo '</table>';
                        wp_reset_postdata();
                    } else {
                        echo '<p>' . __('No fitness posts found.', 'auto-blog-create') . '</p>';
                    }
                    ?>
                </div>
            </div>

            <div class="abc-admin-sidebar">
                <div class="abc-card">
                    <h3><?php _e('Fitness Page Info', 'auto-blog-create'); ?></h3>
                    <p><?php _e('Your fitness posts are automatically displayed at:', 'auto-blog-create'); ?></p>
                    <p><strong><?php echo home_url('/fitness/'); ?></strong></p>
                    <p><?php _e('You can also use the shortcode:', 'auto-blog-create'); ?></p>
                    <code>[fitness_posts count="5"]</code>
                    <p><?php _e('Available shortcode parameters:', 'auto-blog-create'); ?></p>
                    <ul>
                        <li><code>count</code> - Number of posts to show</li>
                        <li><code>show_excerpt</code> - Show excerpts (true/false)</li>
                        <li><code>show_image</code> - Show featured images (true/false)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Display the travel posts page.
 */
function abc_travel_page() {
    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

        <div class="abc-admin-container">
            <div class="abc-admin-main">
                <div class="abc-card">
                    <h2><?php _e('Travel Posts Overview', 'ai-auto-blog-creator'); ?></h2>
                    <p><?php _e('View and manage all auto-generated travel blog posts.', 'ai-auto-blog-creator'); ?></p>

                    <div class="abc-quick-links">
                        <a href="<?php echo admin_url('admin.php?page=ai-auto-blog-creator-generate'); ?>" class="button button-primary">
                            <?php _e('Generate New Travel Post', 'ai-auto-blog-creator'); ?>
                        </a>
                    </div>
                </div>

                <div class="abc-card">
                    <h2><?php _e('All Travel Posts', 'ai-auto-blog-creator'); ?></h2>
                    <?php
                    $args = array(
                        'post_type'      => 'post',
                        'posts_per_page' => 20,
                        'meta_query'     => array(
                            array(
                                'key'     => '_abc_auto_generated',
                                'value'   => '1',
                                'compare' => '=',
                            ),
                            array(
                                'key'     => '_abc_content_type',
                                'value'   => 'travel',
                                'compare' => '=',
                            ),
                        ),
                    );

                    $query = new WP_Query($args);

                    if ($query->have_posts()) {
                        echo '<table class="wp-list-table widefat fixed striped">';
                        echo '<thead><tr><th>Title</th><th>Destination</th><th>Date</th><th>Status</th><th>Actions</th></tr></thead>';
                        echo '<tbody>';
                        while ($query->have_posts()) {
                            $query->the_post();
                            $destination = get_post_meta(get_the_ID(), '_abc_destination', true);
                            echo '<tr>';
                            echo '<td><strong>' . get_the_title() . '</strong></td>';
                            echo '<td>' . esc_html($destination) . '</td>';
                            echo '<td>' . get_the_date() . '</td>';
                            echo '<td><span class="abc-post-status">' . get_post_status() . '</span></td>';
                            echo '<td>';
                            echo '<a href="' . get_edit_post_link() . '" class="button button-small">Edit</a> ';
                            echo '<a href="' . get_permalink() . '" class="button button-small" target="_blank">View</a>';
                            echo '</td>';
                            echo '</tr>';
                        }
                        echo '</tbody>';
                        echo '</table>';
                        wp_reset_postdata();
                    } else {
                        echo '<p>' . __('No travel posts found.', 'ai-auto-blog-creator') . '</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Display the mental health posts page.
 */
function abc_mental_health_page() {
    ?>
    <div class="wrap">
        <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

        <div class="abc-admin-container">
            <div class="abc-admin-main">
                <div class="abc-card">
                    <h2><?php _e('Mental Health Posts Overview', 'ai-auto-blog-creator'); ?></h2>
                    <p><?php _e('View and manage all auto-generated mental health blog posts.', 'ai-auto-blog-creator'); ?></p>

                    <div class="abc-quick-links">
                        <a href="<?php echo admin_url('admin.php?page=ai-auto-blog-creator-generate'); ?>" class="button button-primary">
                            <?php _e('Generate New Mental Health Post', 'ai-auto-blog-creator'); ?>
                        </a>
                    </div>
                </div>

                <div class="abc-card">
                    <h2><?php _e('All Mental Health Posts', 'ai-auto-blog-creator'); ?></h2>
                    <?php
                    $args = array(
                        'post_type'      => 'post',
                        'posts_per_page' => 20,
                        'meta_query'     => array(
                            array(
                                'key'     => '_abc_auto_generated',
                                'value'   => '1',
                                'compare' => '=',
                            ),
                            array(
                                'key'     => '_abc_content_type',
                                'value'   => 'mental_health',
                                'compare' => '=',
                            ),
                        ),
                    );

                    $query = new WP_Query($args);

                    if ($query->have_posts()) {
                        echo '<table class="wp-list-table widefat fixed striped">';
                        echo '<thead><tr><th>Title</th><th>Topic</th><th>Date</th><th>Status</th><th>Actions</th></tr></thead>';
                        echo '<tbody>';
                        while ($query->have_posts()) {
                            $query->the_post();
                            $topic = get_post_meta(get_the_ID(), '_abc_topic', true);
                            echo '<tr>';
                            echo '<td><strong>' . get_the_title() . '</strong></td>';
                            echo '<td>' . esc_html($topic) . '</td>';
                            echo '<td>' . get_the_date() . '</td>';
                            echo '<td><span class="abc-post-status">' . get_post_status() . '</span></td>';
                            echo '<td>';
                            echo '<a href="' . get_edit_post_link() . '" class="button button-small">Edit</a> ';
                            echo '<a href="' . get_permalink() . '" class="button button-small" target="_blank">View</a>';
                            echo '</td>';
                            echo '</tr>';
                        }
                        echo '</tbody>';
                        echo '</table>';
                        wp_reset_postdata();
                    } else {
                        echo '<p>' . __('No mental health posts found.', 'ai-auto-blog-creator') . '</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
    <?php
}
