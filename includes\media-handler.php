<?php
/**
 * Media handler functions for Auto Blog Create plugin.
 *
 * @package Auto_Blog_Create
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Get a travel image from Pexels API.
 *
 * @param string $query The search query for the image.
 * @return array|bool Image data array or false on failure.
 */
function abc_get_pexels_image($query) {
    // Get Pexels API key
    $api_key = get_option('abc_pexels_api_key');

    if (!$api_key) {
        abc_log_error('Pexels API key not configured.');
        return false;
    }

    // Log the search query for debugging
    abc_log_error('Searching Pexels for: ' . $query);

    // Prepare the API URL for search
    $api_url = add_query_arg(
        array(
            'query'       => urlencode($query . ' travel'),
            'orientation' => 'landscape',
            'per_page'    => 1,
            'page'        => rand(1, 5), // Reduced range for better results
        ),
        'https://api.pexels.com/v1/search'
    );

    // Log the API URL for debugging
    abc_log_error('Pexels API URL: ' . $api_url);

    // Add retry logic
    $max_retries = 3;
    $retry_count = 0;
    $response = null;

    while ($retry_count < $max_retries) {
        // Make the API request with optimized timeout
        $response = wp_remote_get($api_url, array(
            'headers' => array(
                'Authorization' => $api_key,
            ),
            'timeout' => 15, // Reduced timeout
            'sslverify' => false, // Disable SSL verification for faster response
        ));

        // If successful, break the loop
        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            break;
        }

        // Log retry attempt
        abc_log_error('Pexels API retry attempt ' . ($retry_count + 1) . ' of ' . $max_retries);
        
        // Wait before retrying (exponential backoff)
        sleep(pow(2, $retry_count));
        $retry_count++;
    }

    // Check for errors after all retries
    if (is_wp_error($response)) {
        abc_log_error('Pexels API error after ' . $max_retries . ' retries: ' . $response->get_error_message());
        return false;
    }

    // Check response code
    $response_code = wp_remote_retrieve_response_code($response);
    abc_log_error('Pexels API response code: ' . $response_code);

    if ($response_code !== 200) {
        $body = wp_remote_retrieve_body($response);
        abc_log_error('Pexels API non-200 response: ' . $body);
        return false;
    }

    // Parse the response
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    // Log the raw response for debugging
    abc_log_error('Pexels API response: ' . substr($body, 0, 500) . '...');

    // Check if we got a valid response with photos
    if (empty($data) || !isset($data['photos']) || empty($data['photos'])) {
        abc_log_error('No images found from Pexels API for query: ' . $query . '. Response: ' . $body);
        return false;
    }

    // Get the first photo from results
    $photo = $data['photos'][0];

    // Validate photo data
    if (!isset($photo['src']) || !isset($photo['photographer'])) {
        abc_log_error('Invalid photo data from Pexels API: ' . json_encode($photo));
        return false;
    }

    // Log successful image retrieval
    abc_log_error('Successfully retrieved Pexels image: ' . $photo['id'] . ' by ' . $photo['photographer']);

    // Return the image data in consistent format
    return array(
        'url'         => $photo['src']['large'],
        'download_url' => $photo['src']['original'],
        'author'      => $photo['photographer'],
        'author_url'  => $photo['photographer_url'],
        'alt_text'    => !empty($photo['alt']) ? $photo['alt'] : $query . ' travel destination',
        'location'    => '', // Pexels doesn't provide location data
        'pexels_id'   => $photo['id'],
        'pexels_url'  => $photo['url'],
    );
}

/**
 * Get a fallback image when Unsplash API fails.
 *
 * @param string $query The search query for the image.
 * @return array Fallback image data.
 */
function abc_get_fallback_image($query) {
    // Use Wikimedia Commons API as a fallback
    $api_url = 'https://commons.wikimedia.org/w/api.php';

    $args = array(
        'action'    => 'query',
        'generator' => 'search',
        'gsrsearch' => $query . ' travel',
        'gsrlimit'  => 1,
        'prop'      => 'imageinfo',
        'iiprop'    => 'url|user',
        'format'    => 'json',
    );

    $url = add_query_arg($args, $api_url);
    $response = wp_remote_get($url, array(
        'headers' => array(
            'User-Agent' => 'WordPress/Auto-Blog-Create-Plugin',
        ),
    ));

    if (!is_wp_error($response)) {
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!empty($data['query']['pages'])) {
            $page = reset($data['query']['pages']);
            if (isset($page['imageinfo'][0]['url'])) {
                return array(
                    'url'        => $page['imageinfo'][0]['url'],
                    'download_url' => $page['imageinfo'][0]['url'],
                    'author'     => $page['imageinfo'][0]['user'],
                    'author_url' => 'https://commons.wikimedia.org/wiki/User:' . $page['imageinfo'][0]['user'],
                    'alt_text'   => $query . ' travel destination',
                );
            }
        }
    }

    // If all else fails, use a placeholder image
    return array(
        'url'        => 'https://via.placeholder.com/800x600?text=' . urlencode($query),
        'download_url' => 'https://via.placeholder.com/800x600?text=' . urlencode($query),
        'author'     => 'Placeholder',
        'author_url' => 'https://placeholder.com',
        'alt_text'   => $query . ' travel destination',
    );
}

/**
 * Download and attach an image to a post.
 *
 * @param int $post_id The post ID to attach the image to.
 * @param string $image_url The URL of the image to download.
 * @param string $alt_text The alt text for the image.
 * @param string $caption The caption for the image.
 * @param array $image_data Additional image data from Unsplash.
 * @return int|bool The attachment ID on success, false on failure.
 */
function abc_attach_image_to_post($post_id, $image_url, $alt_text, $caption, $image_data = array()) {
    // Log the image attachment attempt
    abc_log_error('Attempting to attach image to post ' . $post_id . ': ' . $image_url);

    // Make sure the required functions are available
    if (!function_exists('media_sideload_image')) {
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
    }

    // Download and attach the image
    $attachment_id = media_sideload_image($image_url, $post_id, $alt_text, 'id');

    // Check for errors
    if (is_wp_error($attachment_id)) {
        abc_log_error('Error attaching image: ' . $attachment_id->get_error_message());
        abc_log_error('Image URL that failed: ' . $image_url);
        return false;
    }

    // Log successful attachment
    abc_log_error('Successfully attached image with ID: ' . $attachment_id);

    // Set the image as the featured image
    $thumbnail_result = set_post_thumbnail($post_id, $attachment_id);
    abc_log_error('Set featured image result: ' . ($thumbnail_result ? 'success' : 'failed'));

    // Update attachment metadata
    wp_update_post(array(
        'ID'           => $attachment_id,
        'post_excerpt' => $caption,
    ));

    // Update alt text
    update_post_meta($attachment_id, '_wp_attachment_image_alt', $alt_text);

    // Store Pexels attribution data if available
    if (!empty($image_data)) {
        if (isset($image_data['author'])) {
            update_post_meta($attachment_id, '_abc_image_author', $image_data['author']);
        }

        if (isset($image_data['author_url'])) {
            update_post_meta($attachment_id, '_abc_image_author_url', $image_data['author_url']);
        }

        if (isset($image_data['location'])) {
            update_post_meta($attachment_id, '_abc_image_location', $image_data['location']);
        }

        if (isset($image_data['pexels_id'])) {
            update_post_meta($attachment_id, '_abc_pexels_id', $image_data['pexels_id']);
        }

        if (isset($image_data['pexels_url'])) {
            update_post_meta($attachment_id, '_abc_pexels_url', $image_data['pexels_url']);
        }

        // Mark as Pexels image
        update_post_meta($attachment_id, '_abc_image_source', 'pexels');
    }

    return $attachment_id;
}

/**
 * Process shortcodes in the content.
 *
 * @param string $content The post content.
 * @param string $destination The travel destination.
 * @return string The processed content.
 */
function abc_process_shortcodes($content, $destination) {
    // Replace [travel_map location="X"] shortcode
    $content = preg_replace_callback(
        '/\[travel_map location="([^"]+)"\]/',
        function($matches) {
            $location = $matches[1];
            $map_embed = '<div class="abc-map-container">';
            $map_embed .= '<iframe width="100%" height="450" frameborder="0" style="border:0" ';
            $map_embed .= 'src="https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=' . urlencode($location) . '" ';
            $map_embed .= 'allowfullscreen></iframe>';
            $map_embed .= '<p class="abc-map-caption">Map of ' . esc_html($location) . '</p>';
            $map_embed .= '</div>';

            return $map_embed;
        },
        $content
    );

    // Add a map shortcode if none exists
    if (strpos($content, '[travel_map') === false) {
        $content .= "\n\n[travel_map location=\"$destination\"]";
    }

    return $content;
}

/**
 * Test the Pexels API connection.
 *
 * @return array Result of the test with 'success' boolean and 'message' string.
 */
function abc_test_pexels_api() {
    $api_key = get_option('abc_pexels_api_key');

    if (!$api_key) {
        return array(
            'success' => false,
            'message' => 'Pexels API key not configured.',
        );
    }

    // Test with a simple search query
    $api_url = add_query_arg(
        array(
            'query'    => 'travel',
            'per_page' => 1,
        ),
        'https://api.pexels.com/v1/search'
    );

    $response = wp_remote_get($api_url, array(
        'headers' => array(
            'Authorization' => $api_key,
        ),
    ));

    if (is_wp_error($response)) {
        return array(
            'success' => false,
            'message' => 'Error: ' . $response->get_error_message(),
        );
    }

    $code = wp_remote_retrieve_response_code($response);

    if ($code !== 200) {
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if ($code === 401) {
            return array(
                'success' => false,
                'message' => 'Invalid API key. Please check your Pexels API key.',
            );
        }

        $error_message = isset($data['error']) ? $data['error'] : 'Unknown error';
        return array(
            'success' => false,
            'message' => "API returned code $code: $error_message",
        );
    }

    // Verify we can parse the response
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    if (!$data || !isset($data['photos'])) {
        return array(
            'success' => false,
            'message' => 'Invalid response format from Pexels API.',
        );
    }

    if (empty($data['photos'])) {
        return array(
            'success' => true,
            'message' => 'Connection successful! API is working but no photos found for test query.',
        );
    }

    $photographer = $data['photos'][0]['photographer'];
    return array(
        'success' => true,
        'message' => 'Connection successful! Retrieved image by ' . $photographer,
    );
}
