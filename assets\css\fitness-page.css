/* Fitness Page Styles */
.abc-fitness-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.abc-fitness-page h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 10px;
    font-size: 2.5em;
}

.abc-fitness-page > p {
    text-align: center;
    color: #7f8c8d;
    font-size: 1.2em;
    margin-bottom: 40px;
}

.abc-fitness-posts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.abc-fitness-post {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.abc-fitness-post:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.abc-fitness-post h2 {
    margin: 0;
    padding: 20px 20px 10px;
    font-size: 1.4em;
}

.abc-fitness-post h2 a {
    color: #2c3e50;
    text-decoration: none;
}

.abc-fitness-post h2 a:hover {
    color: #e74c3c;
}

.abc-post-meta {
    padding: 0 20px;
    color: #7f8c8d;
    font-size: 0.9em;
    margin-bottom: 15px;
}

.abc-post-thumbnail {
    margin-bottom: 15px;
}

.abc-post-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.abc-post-excerpt {
    padding: 0 20px;
    color: #555;
    line-height: 1.6;
    margin-bottom: 20px;
}

.abc-read-more {
    display: inline-block;
    margin: 0 20px 20px;
    padding: 10px 20px;
    background: #e74c3c;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.abc-read-more:hover {
    background: #c0392b;
    color: white;
}

.abc-pagination {
    text-align: center;
    margin-top: 40px;
}

.abc-pagination .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 5px;
    background: #ecf0f1;
    color: #2c3e50;
    text-decoration: none;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.abc-pagination .page-numbers:hover,
.abc-pagination .page-numbers.current {
    background: #e74c3c;
    color: white;
}

/* Shortcode Styles */
.abc-fitness-shortcode {
    margin: 20px 0;
}

.abc-fitness-item {
    border-bottom: 1px solid #ecf0f1;
    padding: 20px 0;
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.abc-fitness-item:last-child {
    border-bottom: none;
}

.abc-item-thumbnail {
    flex-shrink: 0;
}

.abc-item-thumbnail img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
}

.abc-fitness-item h3 {
    margin: 0 0 10px;
    font-size: 1.2em;
}

.abc-fitness-item h3 a {
    color: #2c3e50;
    text-decoration: none;
}

.abc-fitness-item h3 a:hover {
    color: #e74c3c;
}

.abc-item-excerpt {
    color: #555;
    line-height: 1.5;
    margin-bottom: 10px;
}

.abc-item-link {
    color: #e74c3c;
    text-decoration: none;
    font-weight: bold;
    font-size: 0.9em;
}

.abc-item-link:hover {
    color: #c0392b;
}

/* Responsive Design */
@media (max-width: 768px) {
    .abc-fitness-page {
        padding: 10px;
    }
    
    .abc-fitness-posts {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .abc-fitness-page h1 {
        font-size: 2em;
    }
    
    .abc-fitness-item {
        flex-direction: column;
        text-align: center;
    }
    
    .abc-item-thumbnail img {
        width: 100px;
        height: 100px;
    }
}

/* Admin Styles */
.abc-admin-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.abc-admin-main {
    flex: 2;
}

.abc-admin-sidebar {
    flex: 1;
}

.abc-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.abc-card h2,
.abc-card h3 {
    margin-top: 0;
    color: #23282d;
}

.abc-quick-links {
    margin-top: 20px;
}

.abc-quick-links .button {
    margin-right: 10px;
}

.abc-recent-posts {
    list-style: none;
    padding: 0;
}

.abc-recent-posts li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.abc-recent-posts li:last-child {
    border-bottom: none;
}

.abc-status-list {
    list-style: none;
    padding: 0;
}

.abc-status-list li {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.abc-status-list li:last-child {
    border-bottom: none;
}

.abc-status-ok {
    color: #46b450;
    font-weight: bold;
}

.abc-status-warning {
    color: #ffb900;
    font-weight: bold;
}

.abc-status-error {
    color: #dc3232;
    font-weight: bold;
}

.abc-post-status {
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 0.8em;
    text-transform: uppercase;
    font-weight: bold;
}

.abc-post-status:contains("publish") {
    background: #46b450;
    color: white;
}

.abc-post-status:contains("draft") {
    background: #ffb900;
    color: white;
}
