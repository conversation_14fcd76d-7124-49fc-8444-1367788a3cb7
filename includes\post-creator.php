<?php
/**
 * Post creator functions for Auto Blog Create plugin.
 *
 * @package Auto_Blog_Create
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

/**
 * Generate a travel blog post.
 *
 * @param string $destination Optional specific destination. If not provided, a random one will be chosen.
 * @return int|bool The post ID on success, false on failure.
 */
function abc_generate_travel_post($destination = '') {
    // Get a random travel destination if none provided
    if (empty($destination)) {
        $destinations = abc_get_travel_destinations();
        $destination = $destinations[array_rand($destinations)];
    }

    // Get travel keywords
    $keywords = get_option('abc_default_travel_keywords', 'travel, destination, vacation, tourism');

    // Generate post title
    $title = abc_generate_seo_title($destination, $keywords, 'travel');

    // Generate post content
    $content = abc_generate_travel_post_content($destination);

    // Optimize content for SEO
    $content = abc_insert_keywords($content, $destination, $keywords);

    // Add internal links
    $content = abc_add_internal_links($content, $destination);

    // Add disclaimer
    $content .= "\n\n<hr>\n<p><em>This travel guide was generated by AI. Always check current travel advisories, visa requirements, and local conditions before traveling.</em></p>";

    // Generate meta description
    $meta_description = abc_generate_meta_description($destination, $content, 'travel');

    // Determine post status based on settings
    $post_status = get_option('abc_human_review', 1) ? 'draft' : 'publish';

    // Create the post
    $post_data = array(
        'post_title'    => $title,
        'post_content'  => $content,
        'post_status'   => $post_status,
        'post_type'     => 'post',
        'post_author'   => get_current_user_id(),
        'post_category' => array(abc_get_travel_category_id()),
    );

    $post_id = wp_insert_post($post_data);

    if (is_wp_error($post_id)) {
        return false;
    }

    // Add post meta
    update_post_meta($post_id, '_abc_auto_generated', 1);
    update_post_meta($post_id, '_abc_content_type', 'travel');
    update_post_meta($post_id, '_abc_destination', $destination);
    update_post_meta($post_id, '_abc_keywords', $keywords);

    // Save content source
    $content_source = get_option('abc_use_wikipedia') ? 'Wikipedia' : 'OpenRouter Deepseek R1';
    update_post_meta($post_id, '_abc_content_source', $content_source);

    // Set Yoast SEO metadata if available
    if (abc_is_yoast_active()) {
        $focus_keyword = trim(explode(',', $keywords)[0]);
        abc_set_yoast_metadata($post_id, $focus_keyword, $meta_description);
    }

    // Get and attach featured image
    $image_query = $destination . ' travel destination';
    abc_log_error('Starting image search for travel post ' . $post_id . ' with query: ' . $image_query);

    $image_data = abc_get_pexels_image($image_query);

    if ($image_data) {
        $source_name = 'Pexels';
        $location_text = !empty($image_data['location']) ? ' in ' . $image_data['location'] : '';
        $caption = 'Photo by <a href="' . esc_url($image_data['author_url']) . '">' . esc_html($image_data['author']) . '</a>' . $location_text . ' on ' . $source_name;

        $attachment_result = abc_attach_image_to_post($post_id, $image_data['download_url'], $image_data['alt_text'], $caption, $image_data);

        if ($attachment_result) {
            abc_log_error('Image successfully attached to travel post ' . $post_id);
        } else {
            abc_log_error('Failed to attach image to travel post ' . $post_id);
        }
    } else {
        abc_log_error('No image data available for travel post ' . $post_id . ' with query: ' . $image_query);
    }

    return $post_id;
}

/**
 * Generate a mental health blog post.
 *
 * @param string $topic Optional specific topic. If not provided, a random one will be chosen.
 * @return int|bool The post ID on success, false on failure.
 */
function abc_generate_mental_health_post($topic = '') {
    // Get a random mental health topic if none provided
    if (empty($topic)) {
        $topics = abc_get_mental_health_topics();
        $topic = $topics[array_rand($topics)];
    }

    // Get mental health keywords
    $keywords = get_option('abc_default_mental_health_keywords', 'mental health, wellness, self-care, therapy');

    // Generate post title
    $title = abc_generate_seo_title($topic, $keywords, 'mental_health');

    // Generate post content
    $content = abc_generate_mental_health_post_content($topic);

    // Optimize content for SEO
    $content = abc_insert_keywords($content, $topic, $keywords);

    // Add internal links
    $content = abc_add_internal_links($content, $topic);

    // Add disclaimer
    $content .= "\n\n<hr>\n<p><em>This mental health guide was generated by AI. For personalized advice, diagnosis, or treatment, please consult with qualified mental health professionals. If you're experiencing a mental health crisis, contact emergency services or a crisis helpline immediately.</em></p>";

    // Generate meta description
    $meta_description = abc_generate_meta_description($topic, $content, 'mental_health');

    // Determine post status based on settings
    $post_status = get_option('abc_human_review', 1) ? 'draft' : 'publish';

    // Create the post
    $post_data = array(
        'post_title'    => $title,
        'post_content'  => $content,
        'post_status'   => $post_status,
        'post_type'     => 'post',
        'post_author'   => get_current_user_id(),
        'post_category' => array(abc_get_mental_health_category_id()),
    );

    $post_id = wp_insert_post($post_data);

    if (is_wp_error($post_id)) {
        return false;
    }

    // Add post meta
    update_post_meta($post_id, '_abc_auto_generated', 1);
    update_post_meta($post_id, '_abc_content_type', 'mental_health');
    update_post_meta($post_id, '_abc_topic', $topic);
    update_post_meta($post_id, '_abc_keywords', $keywords);

    // Save content source
    $content_source = get_option('abc_use_wikipedia') ? 'Wikipedia' : 'OpenRouter Deepseek R1';
    update_post_meta($post_id, '_abc_content_source', $content_source);

    // Set Yoast SEO metadata if available
    if (abc_is_yoast_active()) {
        $focus_keyword = trim(explode(',', $keywords)[0]);
        abc_set_yoast_metadata($post_id, $focus_keyword, $meta_description);
    }

    // Get and attach featured image
    $image_query = $topic . ' mental health wellness';
    abc_log_error('Starting image search for mental health post ' . $post_id . ' with query: ' . $image_query);

    $image_data = abc_get_pexels_image($image_query);

    if ($image_data) {
        $source_name = 'Pexels';
        $location_text = !empty($image_data['location']) ? ' in ' . $image_data['location'] : '';
        $caption = 'Photo by <a href="' . esc_url($image_data['author_url']) . '">' . esc_html($image_data['author']) . '</a>' . $location_text . ' on ' . $source_name;

        $attachment_result = abc_attach_image_to_post($post_id, $image_data['download_url'], $image_data['alt_text'], $caption, $image_data);

        if ($attachment_result) {
            abc_log_error('Image successfully attached to mental health post ' . $post_id);
        } else {
            abc_log_error('Failed to attach image to mental health post ' . $post_id);
        }
    } else {
        abc_log_error('No image data available for mental health post ' . $post_id . ' with query: ' . $image_query);
    }

    return $post_id;
}

/**
 * Generate a fitness blog post (legacy function for backward compatibility).
 *
 * @param string $fitness_topic The fitness topic.
 * @param string $keywords Comma-separated keywords.
 * @return int|bool The post ID on success, false on failure.
 */
function abc_generate_fitness_post($fitness_topic, $keywords) {
    // Generate post title
    $title = abc_generate_seo_title($fitness_topic, $keywords);

    // Generate post content
    $content = abc_generate_fitness_post_content($fitness_topic);

    // Optimize content for SEO
    $content = abc_insert_keywords($content, $fitness_topic, $keywords);

    // Add internal links
    $content = abc_add_internal_links($content, $fitness_topic);

    // Process shortcodes
    $content = abc_process_shortcodes($content, $fitness_topic);

    // Add disclaimer
    $content .= "\n\n<hr>\n<p><em>This post was generated by AI. Always consult with healthcare professionals before starting any new fitness program.</em></p>";

    // Generate meta description
    $meta_description = abc_generate_meta_description($fitness_topic, $content);

    // Determine post status based on settings
    $post_status = get_option('abc_human_review', 1) ? 'draft' : 'publish';

    // Create the post
    $post_data = array(
        'post_title'    => $title,
        'post_content'  => $content,
        'post_status'   => $post_status,
        'post_type'     => 'post',
        'post_author'   => get_current_user_id(),
        'post_category' => array(abc_get_fitness_category_id()),
    );

    // Insert the post
    $post_id = wp_insert_post($post_data);

    if (!$post_id || is_wp_error($post_id)) {
        abc_log_error('Error creating post: ' . ($post_id->get_error_message() ?? 'Unknown error'));
        return false;
    }

    // Add post meta
    update_post_meta($post_id, '_abc_auto_generated', 1);
    update_post_meta($post_id, '_abc_fitness_topic', $fitness_topic);
    update_post_meta($post_id, '_abc_keywords', $keywords);

    // Save content source
    $content_source = get_option('abc_force_fallback') ? 'Wikipedia' : 'Hugging Face API';
    update_post_meta($post_id, '_abc_content_source', $content_source);

    // Set Yoast SEO metadata if available
    if (abc_is_yoast_active()) {
        $focus_keyword = trim(explode(',', $keywords)[0]);
        abc_set_yoast_metadata($post_id, $focus_keyword, $meta_description);
    }

    // Get and attach featured image
    $image_query = $fitness_topic . ' ' . trim(explode(',', $keywords)[0]);
    abc_log_error('Starting image search for post ' . $post_id . ' with query: ' . $image_query);

    $image_data = abc_get_pexels_image($image_query);

    if (!$image_data) {
        abc_log_error('Pexels image failed, trying fallback for: ' . $image_query);
        $image_data = abc_get_fallback_image($image_query);
    }

    if ($image_data) {
        abc_log_error('Image data retrieved successfully, proceeding with attachment');

        // Create caption with location if available
        $location_text = '';
        if (!empty($image_data['location'])) {
            $location_text = ' in ' . esc_html($image_data['location']);
        }

        // Determine the source for attribution
        $source_name = 'Pexels';
        if (isset($image_data['author_url']) && $image_data['author_url'] === 'https://placeholder.com') {
            $source_name = 'Placeholder';
        } elseif (isset($image_data['author_url']) && strpos($image_data['author_url'], 'wikimedia') !== false) {
            $source_name = 'Wikimedia Commons';
        }

        $caption = 'Photo by <a href="' . esc_url($image_data['author_url']) . '">' . esc_html($image_data['author']) . '</a>' . $location_text . ' on ' . $source_name;

        // Pass the full image data to the attachment function
        $attachment_result = abc_attach_image_to_post($post_id, $image_data['download_url'], $image_data['alt_text'], $caption, $image_data);

        if ($attachment_result) {
            abc_log_error('Image successfully attached to post ' . $post_id);
        } else {
            abc_log_error('Failed to attach image to post ' . $post_id);
        }
    } else {
        abc_log_error('No image data available for post ' . $post_id . ' with query: ' . $image_query);
    }

    return $post_id;
}

/**
 * Generate the content for a fitness post.
 *
 * @param string $fitness_topic The fitness topic.
 * @return string The generated post content.
 */
function abc_generate_fitness_post_content($fitness_topic) {
    // Generate content for each section
    $benefits_content = abc_generate_fitness_content($fitness_topic, 'benefits');
    $getting_started_content = abc_generate_fitness_content($fitness_topic, 'getting_started');
    $exercises_content = abc_generate_fitness_content($fitness_topic, 'exercises');
    $nutrition_content = abc_generate_fitness_content($fitness_topic, 'nutrition');
    $common_mistakes_content = abc_generate_fitness_content($fitness_topic, 'common_mistakes');

    // Assemble the post content
    $content = '';

    // Introduction
    $content .= abc_generate_fitness_content($fitness_topic);
    $content .= "\n\n";

    // Benefits
    $content .= "<h2>Health Benefits of " . esc_html($fitness_topic) . "</h2>\n";
    $content .= $benefits_content;
    $content .= "\n\n";

    // Getting Started
    $content .= "<h2>How to Get Started with " . esc_html($fitness_topic) . "</h2>\n";
    $content .= $getting_started_content;
    $content .= "\n\n";

    // Essential Exercises
    $content .= "<h2>Essential Exercises for " . esc_html($fitness_topic) . "</h2>\n";
    $content .= $exercises_content;
    $content .= "\n\n";

    // Nutrition Tips
    $content .= "<h2>Nutrition Guidelines</h2>\n";
    $content .= $nutrition_content;
    $content .= "\n\n";

    // Common Mistakes
    $content .= "<h2>Common Mistakes to Avoid</h2>\n";
    $content .= $common_mistakes_content;
    $content .= "\n\n";

    return $content;
}

/**
 * Generate the content for a travel post.
 *
 * @param string $destination The travel destination.
 * @return string The generated post content.
 */
function abc_generate_travel_post_content($destination) {
    // Generate content for each section
    $introduction_content = abc_generate_ai_content($destination, 'travel', 'introduction');
    $attractions_content = abc_generate_ai_content($destination, 'travel', 'attractions');
    $accommodation_content = abc_generate_ai_content($destination, 'travel', 'accommodation');
    $food_content = abc_generate_ai_content($destination, 'travel', 'food');
    $transportation_content = abc_generate_ai_content($destination, 'travel', 'transportation');
    $budget_content = abc_generate_ai_content($destination, 'travel', 'budget');

    // Assemble the post content
    $content = '';

    // Introduction
    $content .= $introduction_content;
    $content .= "\n\n";

    // Top Attractions
    $content .= "<h2>Top Attractions and Activities</h2>\n";
    $content .= $attractions_content;
    $content .= "\n\n";

    // Where to Stay
    $content .= "<h2>Where to Stay</h2>\n";
    $content .= $accommodation_content;
    $content .= "\n\n";

    // Local Cuisine
    $content .= "<h2>Local Cuisine and Dining</h2>\n";
    $content .= $food_content;
    $content .= "\n\n";

    // Transportation
    $content .= "<h2>Getting There and Around</h2>\n";
    $content .= $transportation_content;
    $content .= "\n\n";

    // Budget Guide
    $content .= "<h2>Budget and Travel Tips</h2>\n";
    $content .= $budget_content;
    $content .= "\n\n";

    return $content;
}

/**
 * Generate the content for a mental health post.
 *
 * @param string $topic The mental health topic.
 * @return string The generated post content.
 */
function abc_generate_mental_health_post_content($topic) {
    // Generate content for each section
    $introduction_content = abc_generate_ai_content($topic, 'mental_health', 'introduction');
    $symptoms_content = abc_generate_ai_content($topic, 'mental_health', 'symptoms');
    $coping_content = abc_generate_ai_content($topic, 'mental_health', 'coping_strategies');
    $professional_content = abc_generate_ai_content($topic, 'mental_health', 'professional_help');
    $lifestyle_content = abc_generate_ai_content($topic, 'mental_health', 'lifestyle');
    $support_content = abc_generate_ai_content($topic, 'mental_health', 'support');

    // Assemble the post content
    $content = '';

    // Introduction
    $content .= $introduction_content;
    $content .= "\n\n";

    // Understanding the Signs
    $content .= "<h2>Understanding the Signs and Symptoms</h2>\n";
    $content .= $symptoms_content;
    $content .= "\n\n";

    // Coping Strategies
    $content .= "<h2>Effective Coping Strategies</h2>\n";
    $content .= $coping_content;
    $content .= "\n\n";

    // Professional Help
    $content .= "<h2>When to Seek Professional Help</h2>\n";
    $content .= $professional_content;
    $content .= "\n\n";

    // Lifestyle Factors
    $content .= "<h2>Lifestyle and Wellness</h2>\n";
    $content .= $lifestyle_content;
    $content .= "\n\n";

    // Support Systems
    $content .= "<h2>Building Support Systems</h2>\n";
    $content .= $support_content;
    $content .= "\n\n";

    return $content;
}

/**
 * Get or create the travel category ID.
 *
 * @return int The travel category ID.
 */
function abc_get_travel_category_id() {
    $category = get_term_by('name', 'Travel', 'category');

    if ($category) {
        return $category->term_id;
    }

    // Create the category if it doesn't exist
    $term = wp_insert_term('Travel', 'category', array(
        'description' => 'Travel guides, destination information, and travel tips.',
        'slug'        => 'travel',
    ));

    return $term['term_id'] ?? get_option('default_category');
}

/**
 * Get or create the mental health category ID.
 *
 * @return int The mental health category ID.
 */
function abc_get_mental_health_category_id() {
    $category = get_term_by('name', 'Mental Health', 'category');

    if ($category) {
        return $category->term_id;
    }

    // Create the category if it doesn't exist
    $term = wp_insert_term('Mental Health', 'category', array(
        'description' => 'Mental health guides, wellness tips, and self-care information.',
        'slug'        => 'mental-health',
    ));

    return $term['term_id'] ?? get_option('default_category');
}

/**
 * Get or create the fitness category ID.
 *
 * @return int The fitness category ID.
 */
function abc_get_fitness_category_id() {
    $category = get_term_by('name', 'Fitness', 'category');

    if ($category) {
        return $category->term_id;
    }

    // Create the category if it doesn't exist
    $term = wp_insert_term('Fitness', 'category', array(
        'description' => 'Fitness guides, workout tips, and health information.',
        'slug'        => 'fitness',
    ));

    return $term['term_id'] ?? get_option('default_category');
}

/**
 * Update the cron schedule for post generation.
 *
 * @param string $frequency The frequency ('daily', 'weekly', or 'monthly').
 * @return bool True on success, false on failure.
 */
function abc_update_cron_schedule($frequency) {
    // Clear existing schedule
    wp_clear_scheduled_hook('abc_scheduled_post_creation');

    // Set up new schedule
    $result = wp_schedule_event(time(), $frequency, 'abc_scheduled_post_creation');

    // Update option
    update_option('abc_post_frequency', $frequency);

    return $result !== false;
}

/**
 * AJAX handler for updating cron schedule.
 */
function abc_ajax_update_cron_schedule() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'abc_ajax_nonce')) {
        wp_send_json_error(array('message' => __('Security check failed.', 'auto-blog-create')));
    }

    // Get frequency
    $frequency = isset($_POST['frequency']) ? sanitize_text_field($_POST['frequency']) : 'weekly';

    // Update schedule
    $result = abc_update_cron_schedule($frequency);

    if ($result) {
        wp_send_json_success(array('message' => __('Schedule updated successfully!', 'auto-blog-create')));
    } else {
        wp_send_json_error(array('message' => __('Error updating schedule.', 'auto-blog-create')));
    }
}
add_action('wp_ajax_abc_update_cron_schedule', 'abc_ajax_update_cron_schedule');
