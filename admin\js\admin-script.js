/**
 * Admin scripts for Auto Blog Create plugin.
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        // Test API connection buttons
        $('.abc-test-api-button').on('click', function(e) {
            e.preventDefault();

            const $button = $(this);
            const api = $button.data('api');
            const $result = $('#abc-' + api + '-test-result');

            // Show loading indicator
            $button.prop('disabled', true);
            $button.after('<span class="abc-loading"></span>');

            // Clear previous results
            $result.removeClass('abc-api-test-success abc-api-test-error').empty();

            // Send AJAX request
            $.ajax({
                url: abcData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'abc_test_api_connection',
                    nonce: abcData.nonce,
                    api: api
                },
                success: function(response) {
                    if (response.success) {
                        $result.addClass('abc-api-test-success').text(response.data.message);
                    } else {
                        $result.addClass('abc-api-test-error').text(response.data.message);
                    }
                },
                error: function() {
                    $result.addClass('abc-api-test-error').text('Connection error. Please try again.');
                },
                complete: function() {
                    // Remove loading indicator and re-enable button
                    $button.prop('disabled', false);
                    $button.siblings('.abc-loading').remove();
                }
            });
        });

        // Add test API buttons to settings page
        if ($('body').hasClass('auto-blog-create_page_auto-blog-create-settings')) {
            // Add test button for Hugging Face API
            $('input[name="abc_huggingface_model"]').closest('tr').find('td .description').after(
                '<p><button class="button abc-test-api-button" data-api="huggingface">Test Hugging Face API</button>' +
                '<div id="abc-huggingface-test-result" class="abc-api-test-result"></div></p>'
            );

            // Add test button for Pexels API
            $('input[name="abc_pexels_api_key"]').closest('tr').find('td .description').after(
                '<p><button class="button abc-test-api-button" data-api="pexels">Test Pexels API</button>' +
                '<div id="abc-pexels-test-result" class="abc-api-test-result"></div></p>'
            );
        }

        // Handle post frequency change
        $('select[name="abc_post_frequency"]').on('change', function() {
            const frequency = $(this).val();

            // Update cron schedule
            $.ajax({
                url: abcData.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'abc_update_cron_schedule',
                    nonce: abcData.nonce,
                    frequency: frequency
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        const $message = $('<div class="notice notice-success is-dismissible"><p>' + response.data.message + '</p></div>');
                        $('.wrap h1').after($message);

                        // Auto-dismiss after 3 seconds
                        setTimeout(function() {
                            $message.fadeOut(function() {
                                $(this).remove();
                            });
                        }, 3000);
                    }
                }
            });
        });
    });
})(jQuery);
