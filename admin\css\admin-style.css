/**
 * Admin styles for Auto Blog Create plugin.
 */

.abc-admin-container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
}

.abc-admin-main {
    flex: 2;
    margin-right: 20px;
}

.abc-admin-sidebar {
    flex: 1;
    min-width: 250px;
}

.abc-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    margin-bottom: 20px;
    padding: 15px;
}

.abc-card h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.abc-quick-links {
    margin-top: 20px;
}

.abc-quick-links .button {
    margin-right: 10px;
}

.abc-recent-posts {
    list-style: disc;
    margin-left: 20px;
}

.abc-recent-posts li {
    margin-bottom: 8px;
}

.abc-post-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    background: #f0f0f0;
}

.abc-status-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.abc-status-list li {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
}

.abc-status-label {
    font-weight: 600;
}

.abc-status-ok {
    color: #46b450;
}

.abc-status-warning {
    color: #ffb900;
}

.abc-status-error {
    color: #dc3232;
}

/* Form styles */
.abc-form-section {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.abc-form-section:last-child {
    border-bottom: none;
}

.abc-form-section h3 {
    margin-top: 0;
}

/* Loading indicator */
.abc-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-left-color: #007cba;
    border-radius: 50%;
    animation: abc-spin 1s linear infinite;
}

@keyframes abc-spin {
    to {
        transform: rotate(360deg);
    }
}

/* API test results */
.abc-api-test-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 3px;
}

.abc-api-test-success {
    background-color: #f0f8e5;
    border: 1px solid #7ad03a;
}

.abc-api-test-error {
    background-color: #fef1f1;
    border: 1px solid #dc3232;
}

/* Post generation form */
.abc-generate-form {
    max-width: 800px;
}

.abc-generate-form .form-table th {
    width: 200px;
}

/* Responsive styles */
@media screen and (max-width: 782px) {
    .abc-admin-container {
        flex-direction: column;
    }

    .abc-admin-main {
        margin-right: 0;
    }
}
